<!DOCTYPE html>
<html>
<head>
    <title>Test Authentication</title>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
</head>
<body>
    <h1>Test Authentication</h1>
    <div id="status"></div>
    <button onclick="testSignup()">Test Signup</button>
    <button onclick="testLogin()">Test Login</button>
    <button onclick="checkSession()">Check Session</button>
    
    <script>
        const supabaseUrl = 'https://ltmmbkckohkusutbsbsu.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx0bW1ia2Nrb2hrdXN1dGJzYnN1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkzNjQzOTYsImV4cCI6MjA2NDk0MDM5Nn0._L2_jT-3A0pCNWuZbcSvNsx_pOp9644MrlI4pTqYpCA';
        
        const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);
        
        function log(message) {
            document.getElementById('status').innerHTML += '<p>' + message + '</p>';
            console.log(message);
        }
        
        async function testSignup() {
            log('Testing signup...');
            const { data, error } = await supabase.auth.signUp({
                email: '<EMAIL>',
                password: 'testpassword123',
                options: {
                    data: {
                        full_name: 'Test User'
                    }
                }
            });
            
            if (error) {
                log('Signup error: ' + error.message);
            } else {
                log('Signup success: ' + JSON.stringify(data));
            }
        }
        
        async function testLogin() {
            log('Testing login...');
            const { data, error } = await supabase.auth.signInWithPassword({
                email: '<EMAIL>',
                password: 'testpassword123'
            });
            
            if (error) {
                log('Login error: ' + error.message);
            } else {
                log('Login success: ' + JSON.stringify(data));
            }
        }
        
        async function checkSession() {
            log('Checking session...');
            const { data: { session }, error } = await supabase.auth.getSession();
            
            if (error) {
                log('Session error: ' + error.message);
            } else {
                log('Session: ' + JSON.stringify(session));
            }
        }
        
        // Check initial session
        checkSession();
    </script>
</body>
</html>
