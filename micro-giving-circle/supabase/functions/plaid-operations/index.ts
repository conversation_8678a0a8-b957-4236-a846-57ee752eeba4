
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { action, ...body } = await req.json();
    console.log('Plaid operation request:', { action, body });

    const PLAID_CLIENT_ID = Deno.env.get('PLAID_CLIENT_ID');
    const PLAID_SECRET = Deno.env.get('PLAID_SECRET');
    const PLAID_ENV = 'sandbox'; // Use 'development' or 'production' for live

    console.log('Environment check:', {
      hasClientId: !!PLAID_CLIENT_ID,
      hasSecret: !!PLAID_SECRET,
      clientIdLength: PLAID_CLIENT_ID?.length,
      secretLength: PLAID_SECRET?.length,
      clientIdPreview: PLAID_CLIENT_ID?.substring(0, 8) + '...',
      secretPreview: PLAID_SECRET?.substring(0, 8) + '...',
      plaidEnv: PLAID_ENV
    });

    if (!PLAID_CLIENT_ID || !PLAID_SECRET) {
      console.error('Missing Plaid credentials:', {
        hasClientId: !!PLAID_CLIENT_ID,
        hasSecret: !!PLAID_SECRET
      });
      throw new Error('Plaid credentials not configured');
    }

    // Validate credential format
    if (PLAID_CLIENT_ID.length < 10 || PLAID_SECRET.length < 10) {
      console.error('Invalid credential format:', {
        clientIdLength: PLAID_CLIENT_ID.length,
        secretLength: PLAID_SECRET.length
      });
      throw new Error('Invalid Plaid credential format');
    }

    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? Deno.env.get('SERVICE_ROLE_KEY') ?? ''
    );

    // Get the user from the Authorization header
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      throw new Error('No authorization header');
    }

    const { data: { user }, error: authError } = await supabaseClient.auth.getUser(
      authHeader.replace('Bearer ', '')
    );

    if (authError || !user) {
      throw new Error('Authentication failed');
    }

    switch (action) {
      case 'create_link_token': {
        console.log('Creating link token for user:', user.id);
        
        const linkTokenRequest = {
          client_id: PLAID_CLIENT_ID,
          secret: PLAID_SECRET,
          client_name: 'GiveRound',
          country_codes: ['US'],
          language: 'en',
          user: {
            client_user_id: user.id,
          },
          products: ['transactions'],
          account_filters: {
            depository: {
              account_subtypes: ['checking', 'savings'],
            },
            credit: {
              account_subtypes: ['credit card'],
            },
          },
        };

        console.log('Link token request payload:', {
          ...linkTokenRequest,
          client_id: PLAID_CLIENT_ID.substring(0, 8) + '...',
          secret: PLAID_SECRET.substring(0, 8) + '...'
        });

        const response = await fetch(`https://${PLAID_ENV}.plaid.com/link/token/create`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(linkTokenRequest),
        });

        const data = await response.json();
        console.log('Link token response:', data);

        if (!response.ok) {
          console.error('Plaid API error details:', {
            status: response.status,
            statusText: response.statusText,
            data
          });
          throw new Error(`Plaid API error (${response.status}): ${data.error_message || data.error_code || 'Unknown error'}`);
        }

        return new Response(JSON.stringify({ link_token: data.link_token }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }

      case 'exchange_public_token': {
        const { public_token } = body;
        console.log('Exchanging public token for user:', user.id);

        const exchangeRequest = {
          client_id: PLAID_CLIENT_ID,
          secret: PLAID_SECRET,
          public_token,
        };

        const response = await fetch(`https://${PLAID_ENV}.plaid.com/item/public_token/exchange`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(exchangeRequest),
        });

        const data = await response.json();
        console.log('Token exchange response:', data);

        if (!response.ok) {
          throw new Error(`Plaid API error: ${data.error_message || 'Unknown error'}`);
        }

        const { access_token, item_id } = data;

        // Get account information
        const accountsRequest = {
          client_id: PLAID_CLIENT_ID,
          secret: PLAID_SECRET,
          access_token,
        };

        const accountsResponse = await fetch(`https://${PLAID_ENV}.plaid.com/accounts/get`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(accountsRequest),
        });

        const accountsData = await accountsResponse.json();
        console.log('Accounts data:', accountsData);

        if (!accountsResponse.ok) {
          throw new Error(`Plaid API error: ${accountsData.error_message || 'Unknown error'}`);
        }

        // Store linked accounts in database
        const accountInserts = accountsData.accounts.map((account: any) => ({
          user_id: user.id,
          plaid_access_token: access_token,
          plaid_item_id: item_id,
          plaid_account_id: account.account_id,
          account_name: account.name,
          account_type: account.type,
          account_subtype: account.subtype,
          mask: account.mask,
          is_active: true,  // Explicitly set as active
        }));

        const { error: insertError } = await supabaseClient
          .from('linked_accounts')
          .insert(accountInserts);

        if (insertError) {
          console.error('Database insert error:', insertError);
          throw new Error('Failed to save account information');
        }

        return new Response(JSON.stringify({ 
          success: true, 
          accounts: accountsData.accounts.length 
        }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }

      case 'fetch_transactions': {
        console.log('Fetching transactions for user:', user.id);

        // Get all linked accounts for the user that are active and have rounding enabled
        const { data: linkedAccounts, error: accountsError } = await supabaseClient
          .from('linked_accounts')
          .select('plaid_access_token, plaid_account_id, account_name, mask, rounding_enabled')
          .eq('user_id', user.id)
          .eq('is_active', true)
          .eq('rounding_enabled', true)
          .is('deleted_at', null);

        if (accountsError) {
          console.error('Error fetching linked accounts:', accountsError);
          throw new Error('Failed to fetch linked accounts');
        }

        if (!linkedAccounts || linkedAccounts.length === 0) {
          // Check if there are any accounts at all (including paused ones)
          const { data: allAccounts } = await supabaseClient
            .from('linked_accounts')
            .select('account_name, rounding_enabled, is_active, deleted_at')
            .eq('user_id', user.id)
            .is('deleted_at', null);

          const pausedCount = allAccounts?.filter(acc => !acc.rounding_enabled).length || 0;
          const inactiveCount = allAccounts?.filter(acc => !acc.is_active).length || 0;

          console.log('No active accounts with rounding enabled found. Summary:', {
            total_accounts: allAccounts?.length || 0,
            paused_accounts: pausedCount,
            inactive_accounts: inactiveCount
          });

          return new Response(JSON.stringify({
            success: true,
            message: `No active accounts with rounding enabled found. ${pausedCount} paused, ${inactiveCount} inactive.`,
            transactions: [],
            account_summary: {
              total: allAccounts?.length || 0,
              paused: pausedCount,
              inactive: inactiveCount,
              processing: 0
            }
          }), {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          });
        }

        console.log('Found active accounts with rounding enabled:', linkedAccounts.length);

        let allTransactions: any[] = [];

        // Fetch transactions for each linked account
        for (const account of linkedAccounts) {
          try {
            console.log('Processing account:', {
              name: account.account_name,
              rounding_enabled: account.rounding_enabled,
              account_id: account.plaid_account_id
            });

            // Double-check that rounding is enabled for this account
            if (!account.rounding_enabled) {
              console.log('Skipping account with rounding disabled:', account.account_name);
              continue;
            }

            // Calculate date range (last 30 days)
            const endDate = new Date();
            const startDate = new Date();
            startDate.setDate(endDate.getDate() - 30);

            const transactionsRequest = {
              client_id: PLAID_CLIENT_ID,
              secret: PLAID_SECRET,
              access_token: account.plaid_access_token,
              start_date: startDate.toISOString().split('T')[0], // Format: YYYY-MM-DD
              end_date: endDate.toISOString().split('T')[0], // Format: YYYY-MM-DD
              account_ids: [account.plaid_account_id],
              count: 100,
              offset: 0
            };

            console.log('Transaction request for account:', account.account_name, {
              start_date: transactionsRequest.start_date,
              end_date: transactionsRequest.end_date,
              account_id: account.plaid_account_id
            });

            const response = await fetch(`https://${PLAID_ENV}.plaid.com/transactions/get`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(transactionsRequest),
            });

            const data = await response.json();
            console.log('Plaid transactions response for account:', account.account_name, data);

            if (!response.ok) {
              console.error('Plaid API error for account:', account.account_name, data);
              continue; // Skip this account and continue with others
            }

            // Process and store transactions
            const processedTransactions = data.transactions
              .filter((transaction: any) => {
                // Only process debit transactions (purchases) - positive amounts in Plaid
                return transaction.amount > 0;
              })
              .map((transaction: any) => {
                const purchaseAmount = Math.abs(transaction.amount);
                const roundUpAmount = Math.ceil(purchaseAmount) - purchaseAmount;

                console.log('Processing transaction:', {
                  name: transaction.name,
                  amount: transaction.amount,
                  purchaseAmount,
                  roundUpAmount
                });

                return {
                  user_id: user.id,
                  plaid_transaction_id: transaction.transaction_id,
                  vendor_name: transaction.merchant_name || transaction.name || 'Unknown Merchant',
                  purchase_amount: purchaseAmount,
                  round_up_amount: roundUpAmount,
                  transaction_date: transaction.date,
                  card_last_four: account.mask,
                  processed: false,
                  account_id: account.plaid_account_id
                };
              })
              .filter((t: any) => t.round_up_amount > 0); // Only include transactions with round-ups

            allTransactions = allTransactions.concat(processedTransactions);

          } catch (accountError) {
            console.error('Error processing account:', account.account_name, accountError);
            continue; // Skip this account and continue with others
          }
        }

        console.log('Total processed transactions:', allTransactions.length);

        // Store transactions in database
        if (allTransactions.length > 0) {
          console.log('Inserting transactions into database:', allTransactions.length);

          // Try to insert transactions, handling duplicates gracefully
          const { data: insertData, error: insertError } = await supabaseClient
            .from('transactions')
            .insert(allTransactions)
            .select();

          if (insertError) {
            console.error('Database insert error:', insertError);
            // If it's a duplicate key error, that's okay - just log it
            if (insertError.code === '23505') {
              console.log('Some transactions already exist (duplicate key), continuing...');
            } else {
              throw new Error(`Failed to save transactions: ${insertError.message}`);
            }
          } else {
            console.log('Successfully inserted transactions:', insertData?.length || 0);
          }
        }

        return new Response(JSON.stringify({
          success: true,
          transactions_processed: allTransactions.length,
          transactions: allTransactions,
          accounts_processed: linkedAccounts.length,
          message: `Processed ${allTransactions.length} transactions from ${linkedAccounts.length} active accounts with rounding enabled.`
        }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }

      default:
        throw new Error(`Unknown action: ${action}`);
    }
  } catch (error) {
    console.error('Plaid operation error:', error);
    return new Response(JSON.stringify({
      error: error.message || 'Internal server error'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});
