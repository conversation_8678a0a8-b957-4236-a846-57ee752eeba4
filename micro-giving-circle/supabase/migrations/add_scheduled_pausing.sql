-- Add scheduled pausing functionality to linked_accounts table
-- This allows users to schedule automatic pause/resume of accounts

-- Add columns for scheduled pausing
ALTER TABLE linked_accounts ADD COLUMN IF NOT EXISTS scheduled_pause_start TIMESTAMP WITH TIME ZONE NULL;
ALTER TABLE linked_accounts ADD COLUMN IF NOT EXISTS scheduled_pause_end TIMESTAMP WITH TIME ZONE NULL;
ALTER TABLE linked_accounts ADD COLUMN IF NOT EXISTS scheduled_pause_reason TEXT NULL;
ALTER TABLE linked_accounts ADD COLUMN IF NOT EXISTS auto_paused BOOLEAN DEFAULT false;

-- Add index for scheduled operations
CREATE INDEX IF NOT EXISTS idx_linked_accounts_scheduled_pause 
ON linked_accounts (scheduled_pause_start, scheduled_pause_end) 
WHERE scheduled_pause_start IS NOT NULL OR scheduled_pause_end IS NOT NULL;

-- Add index for auto-paused accounts
CREATE INDEX IF NOT EXISTS idx_linked_accounts_auto_paused 
ON linked_accounts (auto_paused, scheduled_pause_end) 
WHERE auto_paused = true;

-- Create a function to process scheduled pauses
CREATE OR REPLACE FUNCTION process_scheduled_pauses()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Auto-pause accounts that should be paused now
  UPDATE linked_accounts 
  SET 
    rounding_enabled = false,
    auto_paused = true
  WHERE 
    scheduled_pause_start <= NOW()
    AND scheduled_pause_end > NOW()
    AND rounding_enabled = true
    AND auto_paused = false
    AND deleted_at IS NULL;

  -- Auto-resume accounts that should be resumed now
  UPDATE linked_accounts 
  SET 
    rounding_enabled = true,
    auto_paused = false,
    scheduled_pause_start = NULL,
    scheduled_pause_end = NULL,
    scheduled_pause_reason = NULL
  WHERE 
    scheduled_pause_end <= NOW()
    AND auto_paused = true
    AND deleted_at IS NULL;

  -- Clean up expired schedules that weren't auto-paused
  UPDATE linked_accounts 
  SET 
    scheduled_pause_start = NULL,
    scheduled_pause_end = NULL,
    scheduled_pause_reason = NULL
  WHERE 
    scheduled_pause_end <= NOW()
    AND auto_paused = false
    AND deleted_at IS NULL;
END;
$$;

-- Create a function to schedule account pausing
CREATE OR REPLACE FUNCTION schedule_account_pause(
  account_id UUID,
  user_id UUID,
  pause_start TIMESTAMP WITH TIME ZONE,
  pause_end TIMESTAMP WITH TIME ZONE,
  reason TEXT DEFAULT 'Scheduled pause'
)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result json;
  account_exists boolean;
BEGIN
  -- Check if account exists and belongs to user
  SELECT EXISTS(
    SELECT 1 FROM linked_accounts 
    WHERE id = account_id 
    AND linked_accounts.user_id = schedule_account_pause.user_id
    AND deleted_at IS NULL
  ) INTO account_exists;

  IF NOT account_exists THEN
    RETURN json_build_object(
      'success', false,
      'error', 'Account not found or access denied'
    );
  END IF;

  -- Validate dates
  IF pause_start >= pause_end THEN
    RETURN json_build_object(
      'success', false,
      'error', 'Start date must be before end date'
    );
  END IF;

  IF pause_end <= NOW() THEN
    RETURN json_build_object(
      'success', false,
      'error', 'End date must be in the future'
    );
  END IF;

  -- Update the account with scheduled pause
  UPDATE linked_accounts 
  SET 
    scheduled_pause_start = pause_start,
    scheduled_pause_end = pause_end,
    scheduled_pause_reason = reason,
    -- If pause should start immediately, pause now
    rounding_enabled = CASE 
      WHEN pause_start <= NOW() THEN false 
      ELSE rounding_enabled 
    END,
    auto_paused = CASE 
      WHEN pause_start <= NOW() THEN true 
      ELSE auto_paused 
    END
  WHERE id = account_id;

  RETURN json_build_object(
    'success', true,
    'message', 'Scheduled pause set successfully'
  );
END;
$$;

-- Create a function to cancel scheduled pausing
CREATE OR REPLACE FUNCTION cancel_scheduled_pause(
  account_id UUID,
  user_id UUID
)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result json;
  account_exists boolean;
  was_auto_paused boolean;
BEGIN
  -- Check if account exists and belongs to user
  SELECT EXISTS(
    SELECT 1 FROM linked_accounts 
    WHERE id = account_id 
    AND linked_accounts.user_id = cancel_scheduled_pause.user_id
    AND deleted_at IS NULL
  ) INTO account_exists;

  IF NOT account_exists THEN
    RETURN json_build_object(
      'success', false,
      'error', 'Account not found or access denied'
    );
  END IF;

  -- Get current auto_paused status
  SELECT auto_paused INTO was_auto_paused
  FROM linked_accounts 
  WHERE id = account_id;

  -- Cancel the scheduled pause
  UPDATE linked_accounts 
  SET 
    scheduled_pause_start = NULL,
    scheduled_pause_end = NULL,
    scheduled_pause_reason = NULL,
    -- If account was auto-paused, resume it
    rounding_enabled = CASE 
      WHEN was_auto_paused THEN true 
      ELSE rounding_enabled 
    END,
    auto_paused = false
  WHERE id = account_id;

  RETURN json_build_object(
    'success', true,
    'message', 'Scheduled pause cancelled successfully'
  );
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION process_scheduled_pauses() TO authenticated;
GRANT EXECUTE ON FUNCTION schedule_account_pause(UUID, UUID, TIMESTAMP WITH TIME ZONE, TIMESTAMP WITH TIME ZONE, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION cancel_scheduled_pause(UUID, UUID) TO authenticated;

-- Add RLS policies for the new columns
-- (The existing RLS policies on linked_accounts table will cover the new columns)

-- Add comments for documentation
COMMENT ON COLUMN linked_accounts.scheduled_pause_start IS 'When to automatically pause this account';
COMMENT ON COLUMN linked_accounts.scheduled_pause_end IS 'When to automatically resume this account';
COMMENT ON COLUMN linked_accounts.scheduled_pause_reason IS 'Reason for the scheduled pause (e.g., vacation, budget)';
COMMENT ON COLUMN linked_accounts.auto_paused IS 'Whether this account was automatically paused by the scheduler';

COMMENT ON FUNCTION process_scheduled_pauses() IS 'Processes all scheduled pauses and resumes. Should be called periodically.';
COMMENT ON FUNCTION schedule_account_pause(UUID, UUID, TIMESTAMP WITH TIME ZONE, TIMESTAMP WITH TIME ZONE, TEXT) IS 'Schedules an account to be paused for a specific date range';
COMMENT ON FUNCTION cancel_scheduled_pause(UUID, UUID) IS 'Cancels a scheduled pause and optionally resumes the account';
