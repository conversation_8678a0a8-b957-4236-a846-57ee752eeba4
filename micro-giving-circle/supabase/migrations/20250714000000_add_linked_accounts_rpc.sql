-- Create RPC function to get current user ID for debugging
CREATE OR <PERSON><PERSON><PERSON>CE FUNCTION get_current_user_id()
<PERSON><PERSON><PERSON>NS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN auth.uid();
END;
$$;

-- Create RPC function to get linked accounts for the current user
CREATE OR REPLACE FUNCTION get_linked_accounts()
RETURNS TABLE (
  id UUID,
  account_name TEXT,
  account_type TEXT,
  account_subtype TEXT,
  mask TEXT,
  is_active BOOLEAN,
  created_at TIMESTAMPTZ
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if user is authenticated
  IF auth.uid() IS NULL THEN
    RAISE EXCEPTION 'User not authenticated';
  END IF;

  -- Return linked accounts for the current user
  RETURN QUERY
  SELECT 
    la.id,
    la.account_name,
    la.account_type,
    la.account_subtype,
    la.mask,
    la.is_active,
    la.created_at
  FROM linked_accounts la
  WHERE la.user_id = auth.uid()
    AND la.is_active = true
  ORDER BY la.created_at DESC;
END;
$$;
