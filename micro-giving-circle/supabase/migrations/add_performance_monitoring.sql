-- Performance monitoring and metrics for pause/resume operations
-- This tracks operation performance, error rates, and user behavior patterns

-- Create metrics tracking table
CREATE TABLE IF NOT EXISTS operation_metrics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  operation_type VARCHAR(50) NOT NULL, -- 'pause', 'resume', 'bulk_pause', 'bulk_resume', 'schedule', 'cancel_schedule'
  operation_subtype VARCHAR(50), -- 'individual', 'bulk', 'scheduled', 'api'
  account_count INTEGER DEFAULT 1, -- Number of accounts affected
  execution_time_ms INTEGER, -- How long the operation took
  success BOOLEAN NOT NULL,
  error_code VARCHAR(50), -- Error classification if failed
  error_message TEXT, -- Detailed error message
  user_agent TEXT, -- Browser/client info
  ip_address INET, -- User IP for security monitoring
  session_id TEXT, -- Session identifier
  request_id TEXT, -- Unique request identifier
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB -- Additional context data
);

-- Create performance alerts table
CREATE TABLE IF NOT EXISTS performance_alerts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  alert_type VARCHAR(50) NOT NULL, -- 'high_error_rate', 'slow_operations', 'unusual_activity'
  severity VARCHAR(20) NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
  title TEXT NOT NULL,
  description TEXT,
  threshold_value NUMERIC,
  actual_value NUMERIC,
  time_window_minutes INTEGER,
  affected_users INTEGER,
  resolved BOOLEAN DEFAULT false,
  resolved_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB
);

-- Create user behavior patterns table
CREATE TABLE IF NOT EXISTS user_behavior_patterns (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  pattern_type VARCHAR(50) NOT NULL, -- 'frequent_pauser', 'vacation_user', 'bulk_operator', 'scheduler'
  pattern_score NUMERIC(5,2), -- Confidence score 0-100
  first_detected TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  pattern_data JSONB, -- Detailed pattern information
  active BOOLEAN DEFAULT true
);

-- Add indexes for efficient querying
CREATE INDEX IF NOT EXISTS idx_operation_metrics_user_id ON operation_metrics(user_id);
CREATE INDEX IF NOT EXISTS idx_operation_metrics_operation_type ON operation_metrics(operation_type);
CREATE INDEX IF NOT EXISTS idx_operation_metrics_created_at ON operation_metrics(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_operation_metrics_success ON operation_metrics(success);
CREATE INDEX IF NOT EXISTS idx_operation_metrics_execution_time ON operation_metrics(execution_time_ms);

CREATE INDEX IF NOT EXISTS idx_performance_alerts_alert_type ON performance_alerts(alert_type);
CREATE INDEX IF NOT EXISTS idx_performance_alerts_severity ON performance_alerts(severity);
CREATE INDEX IF NOT EXISTS idx_performance_alerts_resolved ON performance_alerts(resolved);
CREATE INDEX IF NOT EXISTS idx_performance_alerts_created_at ON performance_alerts(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_user_behavior_patterns_user_id ON user_behavior_patterns(user_id);
CREATE INDEX IF NOT EXISTS idx_user_behavior_patterns_pattern_type ON user_behavior_patterns(pattern_type);
CREATE INDEX IF NOT EXISTS idx_user_behavior_patterns_active ON user_behavior_patterns(active);

-- Enable RLS
ALTER TABLE operation_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE performance_alerts ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_behavior_patterns ENABLE ROW LEVEL SECURITY;

-- RLS policies for operation_metrics
CREATE POLICY "Users can view their own metrics" ON operation_metrics
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "System can insert metrics" ON operation_metrics
  FOR INSERT WITH CHECK (true); -- Allow system to insert metrics

-- RLS policies for performance_alerts (admin only)
CREATE POLICY "Admins can view all alerts" ON performance_alerts
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.uid() = id 
      AND raw_user_meta_data->>'role' = 'admin'
    )
  );

-- RLS policies for user_behavior_patterns
CREATE POLICY "Users can view their own patterns" ON user_behavior_patterns
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "System can manage patterns" ON user_behavior_patterns
  FOR ALL USING (true); -- Allow system to manage patterns

-- Function to log operation metrics
CREATE OR REPLACE FUNCTION log_operation_metric(
  p_user_id UUID,
  p_operation_type VARCHAR(50),
  p_operation_subtype VARCHAR(50) DEFAULT NULL,
  p_account_count INTEGER DEFAULT 1,
  p_execution_time_ms INTEGER DEFAULT NULL,
  p_success BOOLEAN DEFAULT true,
  p_error_code VARCHAR(50) DEFAULT NULL,
  p_error_message TEXT DEFAULT NULL,
  p_user_agent TEXT DEFAULT NULL,
  p_ip_address INET DEFAULT NULL,
  p_session_id TEXT DEFAULT NULL,
  p_request_id TEXT DEFAULT NULL,
  p_metadata JSONB DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  metric_id UUID;
BEGIN
  INSERT INTO operation_metrics (
    user_id,
    operation_type,
    operation_subtype,
    account_count,
    execution_time_ms,
    success,
    error_code,
    error_message,
    user_agent,
    ip_address,
    session_id,
    request_id,
    metadata
  ) VALUES (
    p_user_id,
    p_operation_type,
    p_operation_subtype,
    p_account_count,
    p_execution_time_ms,
    p_success,
    p_error_code,
    p_error_message,
    p_user_agent,
    p_ip_address,
    p_session_id,
    p_request_id,
    p_metadata
  ) RETURNING id INTO metric_id;

  -- Trigger pattern analysis
  PERFORM analyze_user_behavior(p_user_id);
  
  -- Check for performance issues
  PERFORM check_performance_thresholds();

  RETURN metric_id;
END;
$$;

-- Function to analyze user behavior patterns
CREATE OR REPLACE FUNCTION analyze_user_behavior(p_user_id UUID)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  recent_operations INTEGER;
  pause_frequency NUMERIC;
  bulk_operations INTEGER;
  scheduled_operations INTEGER;
BEGIN
  -- Count recent operations (last 30 days)
  SELECT COUNT(*) INTO recent_operations
  FROM operation_metrics
  WHERE user_id = p_user_id
    AND created_at >= NOW() - INTERVAL '30 days';

  -- Calculate pause frequency
  SELECT COUNT(*) INTO pause_frequency
  FROM operation_metrics
  WHERE user_id = p_user_id
    AND operation_type IN ('pause', 'resume')
    AND created_at >= NOW() - INTERVAL '30 days';

  -- Count bulk operations
  SELECT COUNT(*) INTO bulk_operations
  FROM operation_metrics
  WHERE user_id = p_user_id
    AND operation_subtype = 'bulk'
    AND created_at >= NOW() - INTERVAL '30 days';

  -- Count scheduled operations
  SELECT COUNT(*) INTO scheduled_operations
  FROM operation_metrics
  WHERE user_id = p_user_id
    AND operation_type IN ('schedule', 'cancel_schedule')
    AND created_at >= NOW() - INTERVAL '30 days';

  -- Update or insert behavior patterns
  -- Frequent pauser pattern
  IF pause_frequency >= 10 THEN
    INSERT INTO user_behavior_patterns (user_id, pattern_type, pattern_score, pattern_data)
    VALUES (p_user_id, 'frequent_pauser', LEAST(pause_frequency * 5, 100), 
            jsonb_build_object('pause_count', pause_frequency, 'period_days', 30))
    ON CONFLICT (user_id, pattern_type) 
    DO UPDATE SET 
      pattern_score = LEAST(pause_frequency * 5, 100),
      pattern_data = jsonb_build_object('pause_count', pause_frequency, 'period_days', 30),
      last_updated = NOW();
  END IF;

  -- Bulk operator pattern
  IF bulk_operations >= 3 THEN
    INSERT INTO user_behavior_patterns (user_id, pattern_type, pattern_score, pattern_data)
    VALUES (p_user_id, 'bulk_operator', LEAST(bulk_operations * 15, 100),
            jsonb_build_object('bulk_count', bulk_operations, 'period_days', 30))
    ON CONFLICT (user_id, pattern_type)
    DO UPDATE SET
      pattern_score = LEAST(bulk_operations * 15, 100),
      pattern_data = jsonb_build_object('bulk_count', bulk_operations, 'period_days', 30),
      last_updated = NOW();
  END IF;

  -- Scheduler pattern
  IF scheduled_operations >= 2 THEN
    INSERT INTO user_behavior_patterns (user_id, pattern_type, pattern_score, pattern_data)
    VALUES (p_user_id, 'scheduler', LEAST(scheduled_operations * 25, 100),
            jsonb_build_object('schedule_count', scheduled_operations, 'period_days', 30))
    ON CONFLICT (user_id, pattern_type)
    DO UPDATE SET
      pattern_score = LEAST(scheduled_operations * 25, 100),
      pattern_data = jsonb_build_object('schedule_count', scheduled_operations, 'period_days', 30),
      last_updated = NOW();
  END IF;
END;
$$;

-- Function to check performance thresholds and create alerts
CREATE OR REPLACE FUNCTION check_performance_thresholds()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  error_rate NUMERIC;
  avg_execution_time NUMERIC;
  recent_operations INTEGER;
BEGIN
  -- Check error rate in last hour
  SELECT 
    CASE WHEN COUNT(*) > 0 
    THEN (COUNT(*) FILTER (WHERE NOT success)::NUMERIC / COUNT(*)) * 100 
    ELSE 0 END,
    COUNT(*)
  INTO error_rate, recent_operations
  FROM operation_metrics
  WHERE created_at >= NOW() - INTERVAL '1 hour';

  -- Create alert if error rate is high
  IF error_rate > 10 AND recent_operations >= 10 THEN
    INSERT INTO performance_alerts (
      alert_type, severity, title, description, 
      threshold_value, actual_value, time_window_minutes, affected_users
    )
    SELECT 
      'high_error_rate', 
      CASE WHEN error_rate > 25 THEN 'critical'
           WHEN error_rate > 20 THEN 'high'
           ELSE 'medium' END,
      'High Error Rate Detected',
      'Error rate for pause/resume operations is above threshold',
      10,
      error_rate,
      60,
      (SELECT COUNT(DISTINCT user_id) FROM operation_metrics 
       WHERE created_at >= NOW() - INTERVAL '1 hour' AND NOT success)
    WHERE NOT EXISTS (
      SELECT 1 FROM performance_alerts 
      WHERE alert_type = 'high_error_rate' 
        AND NOT resolved 
        AND created_at >= NOW() - INTERVAL '1 hour'
    );
  END IF;

  -- Check average execution time
  SELECT AVG(execution_time_ms) INTO avg_execution_time
  FROM operation_metrics
  WHERE created_at >= NOW() - INTERVAL '1 hour'
    AND execution_time_ms IS NOT NULL
    AND success = true;

  -- Create alert if operations are slow
  IF avg_execution_time > 5000 THEN -- 5 seconds
    INSERT INTO performance_alerts (
      alert_type, severity, title, description,
      threshold_value, actual_value, time_window_minutes
    )
    SELECT
      'slow_operations',
      CASE WHEN avg_execution_time > 10000 THEN 'high'
           ELSE 'medium' END,
      'Slow Operations Detected',
      'Average execution time for operations is above threshold',
      5000,
      avg_execution_time,
      60
    WHERE NOT EXISTS (
      SELECT 1 FROM performance_alerts
      WHERE alert_type = 'slow_operations'
        AND NOT resolved
        AND created_at >= NOW() - INTERVAL '1 hour'
    );
  END IF;
END;
$$;

-- Function to get performance dashboard data
CREATE OR REPLACE FUNCTION get_performance_dashboard(
  p_time_window_hours INTEGER DEFAULT 24
)
RETURNS TABLE (
  total_operations BIGINT,
  successful_operations BIGINT,
  failed_operations BIGINT,
  error_rate NUMERIC,
  avg_execution_time NUMERIC,
  operations_by_type JSONB,
  active_alerts INTEGER,
  top_errors JSONB
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  WITH metrics_summary AS (
    SELECT 
      COUNT(*) as total_ops,
      COUNT(*) FILTER (WHERE success) as success_ops,
      COUNT(*) FILTER (WHERE NOT success) as failed_ops,
      AVG(execution_time_ms) FILTER (WHERE execution_time_ms IS NOT NULL) as avg_time
    FROM operation_metrics
    WHERE created_at >= NOW() - (p_time_window_hours || ' hours')::INTERVAL
  ),
  ops_by_type AS (
    SELECT jsonb_object_agg(operation_type, op_count) as ops_json
    FROM (
      SELECT operation_type, COUNT(*) as op_count
      FROM operation_metrics
      WHERE created_at >= NOW() - (p_time_window_hours || ' hours')::INTERVAL
      GROUP BY operation_type
    ) t
  ),
  error_summary AS (
    SELECT jsonb_agg(
      jsonb_build_object(
        'error_code', error_code,
        'count', error_count,
        'percentage', ROUND((error_count::NUMERIC / total_errors) * 100, 2)
      )
    ) as errors_json
    FROM (
      SELECT 
        COALESCE(error_code, 'unknown') as error_code,
        COUNT(*) as error_count,
        SUM(COUNT(*)) OVER () as total_errors
      FROM operation_metrics
      WHERE created_at >= NOW() - (p_time_window_hours || ' hours')::INTERVAL
        AND NOT success
      GROUP BY error_code
      ORDER BY COUNT(*) DESC
      LIMIT 5
    ) e
  )
  SELECT 
    ms.total_ops,
    ms.success_ops,
    ms.failed_ops,
    CASE WHEN ms.total_ops > 0 
         THEN ROUND((ms.failed_ops::NUMERIC / ms.total_ops) * 100, 2)
         ELSE 0 END,
    ROUND(ms.avg_time, 2),
    COALESCE(obt.ops_json, '{}'::jsonb),
    (SELECT COUNT(*) FROM performance_alerts WHERE NOT resolved)::INTEGER,
    COALESCE(es.errors_json, '[]'::jsonb)
  FROM metrics_summary ms
  CROSS JOIN ops_by_type obt
  CROSS JOIN error_summary es;
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION log_operation_metric(UUID, VARCHAR(50), VARCHAR(50), INTEGER, INTEGER, BOOLEAN, VARCHAR(50), TEXT, TEXT, INET, TEXT, TEXT, JSONB) TO authenticated;
GRANT EXECUTE ON FUNCTION get_performance_dashboard(INTEGER) TO authenticated;

-- Add unique constraint for user behavior patterns
ALTER TABLE user_behavior_patterns ADD CONSTRAINT unique_user_pattern UNIQUE (user_id, pattern_type);

-- Add comments
COMMENT ON TABLE operation_metrics IS 'Tracks performance metrics for all pause/resume operations';
COMMENT ON TABLE performance_alerts IS 'Stores alerts for performance issues and anomalies';
COMMENT ON TABLE user_behavior_patterns IS 'Tracks user behavior patterns for analytics and optimization';

COMMENT ON FUNCTION log_operation_metric(UUID, VARCHAR(50), VARCHAR(50), INTEGER, INTEGER, BOOLEAN, VARCHAR(50), TEXT, TEXT, INET, TEXT, TEXT, JSONB) IS 'Logs performance metrics for operations';
COMMENT ON FUNCTION analyze_user_behavior(UUID) IS 'Analyzes user behavior patterns based on operation history';
COMMENT ON FUNCTION check_performance_thresholds() IS 'Checks for performance issues and creates alerts';
COMMENT ON FUNCTION get_performance_dashboard(INTEGER) IS 'Returns performance dashboard data for specified time window';
