-- Charity-Giver Connection System Database Foundation
-- Phase 1: Core tables, RLS policies, and functions for charity dashboard

-- =====================================================
-- 1. CHARITY AUTHENTICATION & ACCESS CONTROL
-- =====================================================

-- Create charity representatives table for dashboard access
CREATE TABLE IF NOT EXISTS charity_representatives (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  charity_id INTEGER NOT NULL REFERENCES charities(id) ON DELETE CASCADE,
  email TEXT NOT NULL UNIQUE,
  full_name TEXT NOT NULL,
  role VARCHAR(50) NOT NULL DEFAULT 'admin' CHECK (role IN ('admin', 'viewer', 'editor')),
  is_active BOOLEAN DEFAULT true,
  email_verified BOOLEAN DEFAULT false,
  last_login_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES charity_representatives(id),
  
  -- Ensure each charity can have multiple representatives
  UNIQUE(charity_id, email)
);

-- Create charity authentication sessions
CREATE TABLE IF NOT EXISTS charity_auth_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  representative_id UUID NOT NULL REFERENCES charity_representatives(id) ON DELETE CASCADE,
  charity_id INTEGER NOT NULL REFERENCES charities(id) ON DELETE CASCADE,
  session_token TEXT NOT NULL UNIQUE,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_accessed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 2. ENHANCED DONATION PROCESSING
-- =====================================================

-- Add donation batch processing for efficient charity distribution
CREATE TABLE IF NOT EXISTS donation_batches (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  batch_date DATE NOT NULL DEFAULT CURRENT_DATE,
  total_amount NUMERIC(10,2) NOT NULL DEFAULT 0,
  total_transactions INTEGER NOT NULL DEFAULT 0,
  total_users INTEGER NOT NULL DEFAULT 0,
  processing_status VARCHAR(20) NOT NULL DEFAULT 'pending' 
    CHECK (processing_status IN ('pending', 'processing', 'completed', 'failed')),
  processed_at TIMESTAMP WITH TIME ZONE,
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ensure one batch per day
  UNIQUE(batch_date)
);

-- Enhanced donations table with batch tracking and better status management
ALTER TABLE donations ADD COLUMN IF NOT EXISTS batch_id UUID REFERENCES donation_batches(id);
ALTER TABLE donations ADD COLUMN IF NOT EXISTS processing_fee NUMERIC(10,2) DEFAULT 0;
ALTER TABLE donations ADD COLUMN IF NOT EXISTS net_amount NUMERIC(10,2);
ALTER TABLE donations ADD COLUMN IF NOT EXISTS payment_method VARCHAR(50) DEFAULT 'round_up';
ALTER TABLE donations ADD COLUMN IF NOT EXISTS external_transaction_id TEXT;
ALTER TABLE donations ADD COLUMN IF NOT EXISTS failure_reason TEXT;

-- Update donation status to be more specific
ALTER TABLE donations DROP CONSTRAINT IF EXISTS donations_status_check;
ALTER TABLE donations ADD CONSTRAINT donations_status_check 
  CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'refunded', 'cancelled'));

-- =====================================================
-- 3. PRIVACY-SAFE ANALYTICS TABLES
-- =====================================================

-- Charity analytics aggregated by day (no individual user data)
CREATE TABLE IF NOT EXISTS charity_daily_analytics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  charity_id INTEGER NOT NULL REFERENCES charities(id) ON DELETE CASCADE,
  analytics_date DATE NOT NULL,
  
  -- Donation metrics
  total_donations_count INTEGER DEFAULT 0,
  total_donations_amount NUMERIC(10,2) DEFAULT 0,
  average_donation_amount NUMERIC(10,2) DEFAULT 0,
  
  -- Supporter metrics (aggregated, no individual data)
  active_supporters_count INTEGER DEFAULT 0, -- Users who donated this day
  new_supporters_count INTEGER DEFAULT 0,    -- First-time supporters
  returning_supporters_count INTEGER DEFAULT 0,
  
  -- Allocation metrics
  total_allocation_percentage NUMERIC(5,2) DEFAULT 0, -- Sum of all user allocations
  average_allocation_percentage NUMERIC(5,2) DEFAULT 0,
  
  -- Transaction source breakdown
  round_up_donations_count INTEGER DEFAULT 0,
  round_up_donations_amount NUMERIC(10,2) DEFAULT 0,
  direct_donations_count INTEGER DEFAULT 0,
  direct_donations_amount NUMERIC(10,2) DEFAULT 0,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ensure one record per charity per day
  UNIQUE(charity_id, analytics_date)
);

-- Charity monthly summary analytics
CREATE TABLE IF NOT EXISTS charity_monthly_analytics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  charity_id INTEGER NOT NULL REFERENCES charities(id) ON DELETE CASCADE,
  year INTEGER NOT NULL,
  month INTEGER NOT NULL CHECK (month >= 1 AND month <= 12),
  
  -- Monthly aggregated metrics
  total_donations_count INTEGER DEFAULT 0,
  total_donations_amount NUMERIC(10,2) DEFAULT 0,
  average_daily_donations NUMERIC(10,2) DEFAULT 0,
  peak_donation_day DATE,
  peak_donation_amount NUMERIC(10,2) DEFAULT 0,
  
  -- Supporter growth metrics
  total_supporters_count INTEGER DEFAULT 0,
  new_supporters_count INTEGER DEFAULT 0,
  supporter_retention_rate NUMERIC(5,2) DEFAULT 0,
  
  -- Comparative metrics
  growth_rate_donations NUMERIC(5,2) DEFAULT 0, -- vs previous month
  growth_rate_supporters NUMERIC(5,2) DEFAULT 0,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ensure one record per charity per month
  UNIQUE(charity_id, year, month)
);

-- =====================================================
-- 4. DONATION DISTRIBUTION TRACKING
-- =====================================================

-- Track how round-ups are distributed to charities
CREATE TABLE IF NOT EXISTS donation_distributions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  transaction_id INTEGER REFERENCES transactions(id) ON DELETE SET NULL,
  batch_id UUID REFERENCES donation_batches(id) ON DELETE SET NULL,
  
  -- Distribution details
  total_round_up_amount NUMERIC(10,2) NOT NULL,
  distribution_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Individual charity allocations (stored as JSONB for flexibility)
  charity_allocations JSONB NOT NULL, -- [{"charity_id": 1, "percentage": 50, "amount": 1.25}, ...]
  
  -- Status tracking
  status VARCHAR(20) NOT NULL DEFAULT 'pending' 
    CHECK (status IN ('pending', 'distributed', 'failed', 'cancelled')),
  processed_at TIMESTAMP WITH TIME ZONE,
  error_details JSONB,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 5. INDEXES FOR PERFORMANCE
-- =====================================================

-- Charity representatives indexes
CREATE INDEX IF NOT EXISTS idx_charity_representatives_charity_id ON charity_representatives(charity_id);
CREATE INDEX IF NOT EXISTS idx_charity_representatives_email ON charity_representatives(email);
CREATE INDEX IF NOT EXISTS idx_charity_representatives_active ON charity_representatives(charity_id, is_active);

-- Charity auth sessions indexes
CREATE INDEX IF NOT EXISTS idx_charity_auth_sessions_token ON charity_auth_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_charity_auth_sessions_representative ON charity_auth_sessions(representative_id);
CREATE INDEX IF NOT EXISTS idx_charity_auth_sessions_expires ON charity_auth_sessions(expires_at);

-- Donation batches indexes
CREATE INDEX IF NOT EXISTS idx_donation_batches_date ON donation_batches(batch_date);
CREATE INDEX IF NOT EXISTS idx_donation_batches_status ON donation_batches(processing_status);

-- Enhanced donations indexes
CREATE INDEX IF NOT EXISTS idx_donations_batch_id ON donations(batch_id);
CREATE INDEX IF NOT EXISTS idx_donations_charity_date ON donations(charity_id, donation_date);
CREATE INDEX IF NOT EXISTS idx_donations_user_charity ON donations(user_id, charity_id);
CREATE INDEX IF NOT EXISTS idx_donations_status ON donations(status);

-- Analytics indexes
CREATE INDEX IF NOT EXISTS idx_charity_daily_analytics_charity_date ON charity_daily_analytics(charity_id, analytics_date);
CREATE INDEX IF NOT EXISTS idx_charity_monthly_analytics_charity_period ON charity_monthly_analytics(charity_id, year, month);

-- Distribution tracking indexes
CREATE INDEX IF NOT EXISTS idx_donation_distributions_user ON donation_distributions(user_id);
CREATE INDEX IF NOT EXISTS idx_donation_distributions_batch ON donation_distributions(batch_id);
CREATE INDEX IF NOT EXISTS idx_donation_distributions_date ON donation_distributions(distribution_date);
CREATE INDEX IF NOT EXISTS idx_donation_distributions_status ON donation_distributions(status);

-- =====================================================
-- 6. ROW LEVEL SECURITY POLICIES
-- =====================================================

-- Enable RLS on new tables
ALTER TABLE charity_representatives ENABLE ROW LEVEL SECURITY;
ALTER TABLE charity_auth_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE donation_batches ENABLE ROW LEVEL SECURITY;
ALTER TABLE charity_daily_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE charity_monthly_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE donation_distributions ENABLE ROW LEVEL SECURITY;

-- Charity representatives policies
CREATE POLICY "Charity representatives can view their own data" 
  ON charity_representatives FOR SELECT 
  USING (
    -- Allow if user is authenticated as this representative
    auth.jwt() ->> 'charity_representative_id' = id::text
    OR 
    -- Allow if user is admin of the same charity
    charity_id IN (
      SELECT charity_id FROM charity_representatives 
      WHERE id::text = auth.jwt() ->> 'charity_representative_id' 
      AND role = 'admin'
    )
  );

CREATE POLICY "Charity admins can manage representatives" 
  ON charity_representatives FOR ALL 
  USING (
    charity_id IN (
      SELECT charity_id FROM charity_representatives 
      WHERE id::text = auth.jwt() ->> 'charity_representative_id' 
      AND role = 'admin'
    )
  );

-- Charity analytics policies (read-only for charity representatives)
CREATE POLICY "Charity representatives can view their charity analytics" 
  ON charity_daily_analytics FOR SELECT 
  USING (
    charity_id IN (
      SELECT charity_id FROM charity_representatives 
      WHERE id::text = auth.jwt() ->> 'charity_representative_id'
    )
  );

CREATE POLICY "Charity representatives can view their monthly analytics" 
  ON charity_monthly_analytics FOR SELECT 
  USING (
    charity_id IN (
      SELECT charity_id FROM charity_representatives 
      WHERE id::text = auth.jwt() ->> 'charity_representative_id'
    )
  );

-- Donation batches (read-only for charity representatives)
CREATE POLICY "Charity representatives can view donation batches" 
  ON donation_batches FOR SELECT 
  USING (true); -- All charity reps can see batch processing status

-- Enhanced donations policies (charity representatives can only see aggregated data)
CREATE POLICY "Charity representatives can view their charity donations (aggregated only)" 
  ON donations FOR SELECT 
  USING (
    charity_id IN (
      SELECT charity_id FROM charity_representatives 
      WHERE id::text = auth.jwt() ->> 'charity_representative_id'
    )
    -- Note: Individual user_id should not be exposed to charity representatives
    -- This will be handled by views that aggregate the data
  );

-- Users can still view their own donations
CREATE POLICY "Users can view their own donations" 
  ON donations FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own donations" 
  ON donations FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

-- Distribution tracking policies
CREATE POLICY "Users can view their own distribution records" 
  ON donation_distributions FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "System can create distribution records" 
  ON donation_distributions FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

-- =====================================================
-- 7. UPDATED CHARITY TABLE ENHANCEMENTS
-- =====================================================

-- Add fields to support charity dashboard
ALTER TABLE charities ADD COLUMN IF NOT EXISTS dashboard_enabled BOOLEAN DEFAULT false;
ALTER TABLE charities ADD COLUMN IF NOT EXISTS onboarding_completed BOOLEAN DEFAULT false;
ALTER TABLE charities ADD COLUMN IF NOT EXISTS contact_email TEXT;
ALTER TABLE charities ADD COLUMN IF NOT EXISTS contact_phone TEXT;
ALTER TABLE charities ADD COLUMN IF NOT EXISTS address_line1 TEXT;
ALTER TABLE charities ADD COLUMN IF NOT EXISTS address_line2 TEXT;
ALTER TABLE charities ADD COLUMN IF NOT EXISTS city TEXT;
ALTER TABLE charities ADD COLUMN IF NOT EXISTS state TEXT;
ALTER TABLE charities ADD COLUMN IF NOT EXISTS zip_code TEXT;
ALTER TABLE charities ADD COLUMN IF NOT EXISTS country TEXT DEFAULT 'US';

-- Add charity verification and compliance fields
ALTER TABLE charities ADD COLUMN IF NOT EXISTS tax_exempt_status VARCHAR(20) DEFAULT '501c3';
ALTER TABLE charities ADD COLUMN IF NOT EXISTS verification_documents JSONB;
ALTER TABLE charities ADD COLUMN IF NOT EXISTS compliance_status VARCHAR(20) DEFAULT 'pending' 
  CHECK (compliance_status IN ('pending', 'approved', 'rejected', 'under_review'));
ALTER TABLE charities ADD COLUMN IF NOT EXISTS last_compliance_check TIMESTAMP WITH TIME ZONE;

-- Add charity preferences for dashboard
ALTER TABLE charities ADD COLUMN IF NOT EXISTS dashboard_preferences JSONB DEFAULT '{}';
ALTER TABLE charities ADD COLUMN IF NOT EXISTS notification_preferences JSONB DEFAULT '{}';

-- Update charity totals to be more accurate
CREATE INDEX IF NOT EXISTS idx_charities_dashboard_enabled ON charities(dashboard_enabled);
CREATE INDEX IF NOT EXISTS idx_charities_compliance_status ON charities(compliance_status);
CREATE INDEX IF NOT EXISTS idx_charities_verification ON charities(verified, compliance_status);
