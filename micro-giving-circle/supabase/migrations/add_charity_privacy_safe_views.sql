-- Privacy-Safe Views for Charity Dashboard
-- These views ensure charities can only see aggregated data, never individual user information

-- =====================================================
-- 1. CHARITY DASHBOARD SUMMARY VIEW
-- =====================================================

-- Main dashboard view with key metrics (no individual user data)
CREATE OR REPLACE VIEW charity_dashboard_summary AS
SELECT 
  c.id as charity_id,
  c.name as charity_name,
  c.verified,
  c.rating,
  
  -- Current supporter metrics (aggregated only)
  COALESCE(supporter_stats.total_supporters, 0) as total_supporters,
  COALESCE(supporter_stats.active_supporters_30d, 0) as active_supporters_30d,
  COALESCE(supporter_stats.new_supporters_30d, 0) as new_supporters_30d,
  
  -- Donation metrics
  COALESCE(c.total_raised, 0) as total_raised_all_time,
  COALESCE(donation_stats.total_30d, 0) as total_raised_30d,
  COALESCE(donation_stats.total_7d, 0) as total_raised_7d,
  COALESCE(donation_stats.avg_donation_30d, 0) as avg_donation_30d,
  COALESCE(donation_stats.donation_count_30d, 0) as donation_count_30d,
  
  -- Growth metrics
  COALESCE(growth_stats.supporter_growth_rate, 0) as supporter_growth_rate,
  COALESCE(growth_stats.donation_growth_rate, 0) as donation_growth_rate,
  
  -- Last activity
  donation_stats.last_donation_date,
  
  -- Dashboard status
  c.dashboard_enabled,
  c.onboarding_completed
  
FROM charities c

-- Supporter statistics (no individual user data exposed)
LEFT JOIN (
  SELECT 
    charity_id,
    COUNT(DISTINCT user_id) as total_supporters,
    COUNT(DISTINCT CASE 
      WHEN donation_date >= NOW() - INTERVAL '30 days' 
      THEN user_id 
    END) as active_supporters_30d,
    COUNT(DISTINCT CASE 
      WHEN donation_date >= NOW() - INTERVAL '30 days' 
      AND NOT EXISTS (
        SELECT 1 FROM donations d2 
        WHERE d2.user_id = donations.user_id 
        AND d2.charity_id = donations.charity_id 
        AND d2.donation_date < NOW() - INTERVAL '30 days'
      )
      THEN user_id 
    END) as new_supporters_30d
  FROM donations 
  WHERE status IN ('completed', 'pending')
  GROUP BY charity_id
) supporter_stats ON c.id = supporter_stats.charity_id

-- Donation statistics
LEFT JOIN (
  SELECT 
    charity_id,
    SUM(CASE 
      WHEN donation_date >= NOW() - INTERVAL '30 days' 
      THEN amount ELSE 0 
    END) as total_30d,
    SUM(CASE 
      WHEN donation_date >= NOW() - INTERVAL '7 days' 
      THEN amount ELSE 0 
    END) as total_7d,
    AVG(CASE 
      WHEN donation_date >= NOW() - INTERVAL '30 days' 
      THEN amount 
    END) as avg_donation_30d,
    COUNT(CASE 
      WHEN donation_date >= NOW() - INTERVAL '30 days' 
      THEN 1 
    END) as donation_count_30d,
    MAX(donation_date) as last_donation_date
  FROM donations 
  WHERE status IN ('completed', 'pending')
  GROUP BY charity_id
) donation_stats ON c.id = donation_stats.charity_id

-- Growth rate calculations
LEFT JOIN (
  SELECT 
    charity_id,
    -- Supporter growth rate (current month vs previous month)
    CASE 
      WHEN prev_month_supporters.supporter_count > 0 
      THEN ROUND(
        ((curr_month_supporters.supporter_count - prev_month_supporters.supporter_count) 
         * 100.0 / prev_month_supporters.supporter_count), 2
      )
      ELSE 0 
    END as supporter_growth_rate,
    
    -- Donation growth rate (current month vs previous month)
    CASE 
      WHEN prev_month_donations.donation_amount > 0 
      THEN ROUND(
        ((curr_month_donations.donation_amount - prev_month_donations.donation_amount) 
         * 100.0 / prev_month_donations.donation_amount), 2
      )
      ELSE 0 
    END as donation_growth_rate
    
  FROM charities c2
  
  -- Current month supporters
  LEFT JOIN (
    SELECT 
      charity_id,
      COUNT(DISTINCT user_id) as supporter_count
    FROM donations 
    WHERE DATE_TRUNC('month', donation_date) = DATE_TRUNC('month', CURRENT_DATE)
    AND status IN ('completed', 'pending')
    GROUP BY charity_id
  ) curr_month_supporters ON c2.id = curr_month_supporters.charity_id
  
  -- Previous month supporters
  LEFT JOIN (
    SELECT 
      charity_id,
      COUNT(DISTINCT user_id) as supporter_count
    FROM donations 
    WHERE DATE_TRUNC('month', donation_date) = DATE_TRUNC('month', CURRENT_DATE) - INTERVAL '1 month'
    AND status IN ('completed', 'pending')
    GROUP BY charity_id
  ) prev_month_supporters ON c2.id = prev_month_supporters.charity_id
  
  -- Current month donations
  LEFT JOIN (
    SELECT 
      charity_id,
      SUM(amount) as donation_amount
    FROM donations 
    WHERE DATE_TRUNC('month', donation_date) = DATE_TRUNC('month', CURRENT_DATE)
    AND status IN ('completed', 'pending')
    GROUP BY charity_id
  ) curr_month_donations ON c2.id = curr_month_donations.charity_id
  
  -- Previous month donations
  LEFT JOIN (
    SELECT 
      charity_id,
      SUM(amount) as donation_amount
    FROM donations 
    WHERE DATE_TRUNC('month', donation_date) = DATE_TRUNC('month', CURRENT_DATE) - INTERVAL '1 month'
    AND status IN ('completed', 'pending')
    GROUP BY charity_id
  ) prev_month_donations ON c2.id = prev_month_donations.charity_id
  
) growth_stats ON c.id = growth_stats.charity_id

WHERE c.dashboard_enabled = true;

-- =====================================================
-- 2. CHARITY DONATION TRENDS VIEW
-- =====================================================

-- Daily donation trends for charts (aggregated data only)
CREATE OR REPLACE VIEW charity_donation_trends AS
SELECT 
  charity_id,
  DATE(donation_date) as donation_date,
  COUNT(*) as donation_count,
  SUM(amount) as total_amount,
  AVG(amount) as avg_amount,
  COUNT(DISTINCT user_id) as unique_supporters,
  
  -- Payment method breakdown
  COUNT(CASE WHEN payment_method = 'round_up' THEN 1 END) as round_up_count,
  SUM(CASE WHEN payment_method = 'round_up' THEN amount ELSE 0 END) as round_up_amount,
  COUNT(CASE WHEN payment_method != 'round_up' THEN 1 END) as direct_count,
  SUM(CASE WHEN payment_method != 'round_up' THEN amount ELSE 0 END) as direct_amount
  
FROM donations 
WHERE status IN ('completed', 'pending')
AND donation_date >= CURRENT_DATE - INTERVAL '90 days' -- Last 90 days only
GROUP BY charity_id, DATE(donation_date)
ORDER BY charity_id, donation_date DESC;

-- =====================================================
-- 3. CHARITY SUPPORTER INSIGHTS VIEW
-- =====================================================

-- Supporter insights without exposing individual user data
CREATE OR REPLACE VIEW charity_supporter_insights AS
SELECT 
  charity_id,
  
  -- Supporter segmentation (aggregated counts only)
  COUNT(DISTINCT CASE 
    WHEN supporter_stats.total_donations = 1 
    THEN supporter_stats.user_id 
  END) as one_time_supporters,
  
  COUNT(DISTINCT CASE 
    WHEN supporter_stats.total_donations BETWEEN 2 AND 5 
    THEN supporter_stats.user_id 
  END) as occasional_supporters,
  
  COUNT(DISTINCT CASE 
    WHEN supporter_stats.total_donations > 5 
    THEN supporter_stats.user_id 
  END) as regular_supporters,
  
  -- Donation frequency insights
  AVG(supporter_stats.total_donations) as avg_donations_per_supporter,
  AVG(supporter_stats.avg_donation_amount) as avg_amount_per_supporter,
  
  -- Retention metrics
  COUNT(DISTINCT CASE 
    WHEN supporter_stats.last_donation >= CURRENT_DATE - INTERVAL '30 days' 
    THEN supporter_stats.user_id 
  END) as active_last_30d,
  
  COUNT(DISTINCT CASE 
    WHEN supporter_stats.last_donation >= CURRENT_DATE - INTERVAL '90 days' 
    THEN supporter_stats.user_id 
  END) as active_last_90d,
  
  -- Allocation insights (how much of user's total giving goes to this charity)
  AVG(ucf.allocation_percentage) as avg_allocation_percentage,
  COUNT(DISTINCT ucf.user_id) as users_with_allocations

FROM charities c

-- Supporter statistics per user (aggregated to remove individual identification)
LEFT JOIN (
  SELECT 
    charity_id,
    user_id,
    COUNT(*) as total_donations,
    SUM(amount) as total_amount,
    AVG(amount) as avg_donation_amount,
    MIN(donation_date) as first_donation,
    MAX(donation_date) as last_donation
  FROM donations 
  WHERE status IN ('completed', 'pending')
  GROUP BY charity_id, user_id
) supporter_stats ON c.id = supporter_stats.charity_id

-- User charity follows for allocation insights
LEFT JOIN user_charity_follows ucf ON c.id = ucf.charity_id

WHERE c.dashboard_enabled = true
GROUP BY charity_id;

-- =====================================================
-- 4. CHARITY MONTHLY PERFORMANCE VIEW
-- =====================================================

-- Monthly performance metrics for historical analysis
CREATE OR REPLACE VIEW charity_monthly_performance AS
SELECT 
  charity_id,
  EXTRACT(YEAR FROM donation_date) as year,
  EXTRACT(MONTH FROM donation_date) as month,
  DATE_TRUNC('month', donation_date) as month_start,
  
  -- Monthly totals
  COUNT(*) as total_donations,
  SUM(amount) as total_amount,
  AVG(amount) as avg_donation,
  COUNT(DISTINCT user_id) as unique_supporters,
  
  -- New vs returning supporters
  COUNT(DISTINCT CASE 
    WHEN first_donation_month.month_start = DATE_TRUNC('month', donations.donation_date)
    THEN donations.user_id 
  END) as new_supporters,
  
  COUNT(DISTINCT CASE 
    WHEN first_donation_month.month_start < DATE_TRUNC('month', donations.donation_date)
    THEN donations.user_id 
  END) as returning_supporters,
  
  -- Payment method breakdown
  SUM(CASE WHEN payment_method = 'round_up' THEN amount ELSE 0 END) as round_up_total,
  SUM(CASE WHEN payment_method != 'round_up' THEN amount ELSE 0 END) as direct_total,
  
  -- Daily averages for the month
  SUM(amount) / EXTRACT(DAY FROM (DATE_TRUNC('month', donation_date) + INTERVAL '1 month' - INTERVAL '1 day')) as avg_daily_amount

FROM donations 

-- Get first donation month for each user-charity pair
LEFT JOIN (
  SELECT 
    charity_id,
    user_id,
    DATE_TRUNC('month', MIN(donation_date)) as month_start
  FROM donations 
  WHERE status IN ('completed', 'pending')
  GROUP BY charity_id, user_id
) first_donation_month ON donations.charity_id = first_donation_month.charity_id 
                      AND donations.user_id = first_donation_month.user_id

WHERE status IN ('completed', 'pending')
AND donation_date >= CURRENT_DATE - INTERVAL '24 months' -- Last 2 years
GROUP BY charity_id, EXTRACT(YEAR FROM donation_date), EXTRACT(MONTH FROM donation_date), DATE_TRUNC('month', donation_date)
ORDER BY charity_id, year DESC, month DESC;

-- =====================================================
-- 5. CHARITY ALLOCATION INSIGHTS VIEW
-- =====================================================

-- Insights about how users allocate their giving (aggregated only)
CREATE OR REPLACE VIEW charity_allocation_insights AS
SELECT 
  charity_id,
  
  -- Allocation distribution
  COUNT(*) as total_allocations,
  AVG(allocation_percentage) as avg_allocation_percentage,
  MIN(allocation_percentage) as min_allocation_percentage,
  MAX(allocation_percentage) as max_allocation_percentage,
  
  -- Allocation ranges (percentage of users in each range)
  COUNT(CASE WHEN allocation_percentage <= 25 THEN 1 END) as allocations_0_25_percent,
  COUNT(CASE WHEN allocation_percentage > 25 AND allocation_percentage <= 50 THEN 1 END) as allocations_26_50_percent,
  COUNT(CASE WHEN allocation_percentage > 50 AND allocation_percentage <= 75 THEN 1 END) as allocations_51_75_percent,
  COUNT(CASE WHEN allocation_percentage > 75 THEN 1 END) as allocations_76_100_percent,
  
  -- Users with this charity as primary allocation
  COUNT(CASE WHEN allocation_percentage >= 50 THEN 1 END) as primary_charity_users,
  
  -- Recent allocation changes
  COUNT(CASE WHEN created_at >= CURRENT_DATE - INTERVAL '30 days' THEN 1 END) as new_allocations_30d

FROM user_charity_follows ucf
JOIN charities c ON ucf.charity_id = c.id
WHERE c.dashboard_enabled = true
AND allocation_percentage > 0
GROUP BY charity_id;

-- =====================================================
-- 6. RLS POLICIES FOR VIEWS
-- =====================================================

-- Note: Views inherit RLS from their underlying tables, but we can add additional policies

-- Grant access to charity representatives for their charity's data
GRANT SELECT ON charity_dashboard_summary TO authenticated;
GRANT SELECT ON charity_donation_trends TO authenticated;
GRANT SELECT ON charity_supporter_insights TO authenticated;
GRANT SELECT ON charity_monthly_performance TO authenticated;
GRANT SELECT ON charity_allocation_insights TO authenticated;

-- Create RLS policies for the views (these will filter based on charity representative access)
-- The views will automatically filter to only show data for charities the representative has access to
-- through the existing charity_representatives table and authentication system
