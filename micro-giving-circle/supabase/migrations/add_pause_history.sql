-- Create pause history tracking table
-- This tracks all pause/resume actions for audit and user history

CREATE TABLE IF NOT EXISTS account_pause_history (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  account_id UUID NOT NULL REFERENCES linked_accounts(id) ON DELETE CASCADE,
  action VARCHAR(20) NOT NULL CHECK (action IN ('pause', 'resume', 'schedule', 'cancel_schedule')),
  reason TEXT,
  previous_state BOOLEAN, -- Previous rounding_enabled state
  new_state BOOLEAN, -- New rounding_enabled state
  scheduled_start TIMESTAMP WITH TIME ZONE, -- For scheduled actions
  scheduled_end TIMESTAMP WITH TIME ZONE, -- For scheduled actions
  triggered_by VARCHAR(20) DEFAULT 'user' CHECK (triggered_by IN ('user', 'system', 'scheduled')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB -- For additional context
);

-- Add indexes for efficient querying
CREATE INDEX IF NOT EXISTS idx_account_pause_history_user_id ON account_pause_history(user_id);
CREATE INDEX IF NOT EXISTS idx_account_pause_history_account_id ON account_pause_history(account_id);
CREATE INDEX IF NOT EXISTS idx_account_pause_history_created_at ON account_pause_history(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_account_pause_history_action ON account_pause_history(action);

-- Enable RLS
ALTER TABLE account_pause_history ENABLE ROW LEVEL SECURITY;

-- RLS policies
CREATE POLICY "Users can view their own pause history" ON account_pause_history
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own pause history" ON account_pause_history
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Function to log pause/resume actions
CREATE OR REPLACE FUNCTION log_pause_action(
  p_user_id UUID,
  p_account_id UUID,
  p_action VARCHAR(20),
  p_reason TEXT DEFAULT NULL,
  p_previous_state BOOLEAN DEFAULT NULL,
  p_new_state BOOLEAN DEFAULT NULL,
  p_scheduled_start TIMESTAMP WITH TIME ZONE DEFAULT NULL,
  p_scheduled_end TIMESTAMP WITH TIME ZONE DEFAULT NULL,
  p_triggered_by VARCHAR(20) DEFAULT 'user',
  p_metadata JSONB DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  history_id UUID;
BEGIN
  INSERT INTO account_pause_history (
    user_id,
    account_id,
    action,
    reason,
    previous_state,
    new_state,
    scheduled_start,
    scheduled_end,
    triggered_by,
    metadata
  ) VALUES (
    p_user_id,
    p_account_id,
    p_action,
    p_reason,
    p_previous_state,
    p_new_state,
    p_scheduled_start,
    p_scheduled_end,
    p_triggered_by,
    p_metadata
  ) RETURNING id INTO history_id;

  RETURN history_id;
END;
$$;

-- Function to get pause history for a user
CREATE OR REPLACE FUNCTION get_user_pause_history(
  p_user_id UUID,
  p_limit INTEGER DEFAULT 50,
  p_offset INTEGER DEFAULT 0
)
RETURNS TABLE (
  id UUID,
  account_id UUID,
  account_name TEXT,
  action VARCHAR(20),
  reason TEXT,
  previous_state BOOLEAN,
  new_state BOOLEAN,
  scheduled_start TIMESTAMP WITH TIME ZONE,
  scheduled_end TIMESTAMP WITH TIME ZONE,
  triggered_by VARCHAR(20),
  created_at TIMESTAMP WITH TIME ZONE,
  metadata JSONB
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    h.id,
    h.account_id,
    COALESCE(la.account_name, 'Deleted Account') as account_name,
    h.action,
    h.reason,
    h.previous_state,
    h.new_state,
    h.scheduled_start,
    h.scheduled_end,
    h.triggered_by,
    h.created_at,
    h.metadata
  FROM account_pause_history h
  LEFT JOIN linked_accounts la ON h.account_id = la.id
  WHERE h.user_id = p_user_id
  ORDER BY h.created_at DESC
  LIMIT p_limit
  OFFSET p_offset;
END;
$$;

-- Function to get pause history for a specific account
CREATE OR REPLACE FUNCTION get_account_pause_history(
  p_user_id UUID,
  p_account_id UUID,
  p_limit INTEGER DEFAULT 20
)
RETURNS TABLE (
  id UUID,
  action VARCHAR(20),
  reason TEXT,
  previous_state BOOLEAN,
  new_state BOOLEAN,
  scheduled_start TIMESTAMP WITH TIME ZONE,
  scheduled_end TIMESTAMP WITH TIME ZONE,
  triggered_by VARCHAR(20),
  created_at TIMESTAMP WITH TIME ZONE,
  metadata JSONB
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    h.id,
    h.action,
    h.reason,
    h.previous_state,
    h.new_state,
    h.scheduled_start,
    h.scheduled_end,
    h.triggered_by,
    h.created_at,
    h.metadata
  FROM account_pause_history h
  WHERE h.user_id = p_user_id AND h.account_id = p_account_id
  ORDER BY h.created_at DESC
  LIMIT p_limit;
END;
$$;

-- Trigger function to automatically log pause/resume actions
CREATE OR REPLACE FUNCTION trigger_log_pause_action()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
DECLARE
  action_type VARCHAR(20);
  reason_text TEXT;
BEGIN
  -- Determine action type based on rounding_enabled change
  IF OLD.rounding_enabled IS DISTINCT FROM NEW.rounding_enabled THEN
    IF NEW.rounding_enabled = false AND OLD.rounding_enabled = true THEN
      action_type := 'pause';
      reason_text := CASE 
        WHEN NEW.auto_paused = true THEN 'Automatically paused by schedule'
        ELSE 'Manual pause'
      END;
    ELSIF NEW.rounding_enabled = true AND OLD.rounding_enabled = false THEN
      action_type := 'resume';
      reason_text := CASE 
        WHEN OLD.auto_paused = true THEN 'Automatically resumed after schedule'
        ELSE 'Manual resume'
      END;
    END IF;

    -- Log the action
    PERFORM log_pause_action(
      NEW.user_id,
      NEW.id,
      action_type,
      reason_text,
      OLD.rounding_enabled,
      NEW.rounding_enabled,
      NEW.scheduled_pause_start,
      NEW.scheduled_pause_end,
      CASE WHEN NEW.auto_paused = true OR OLD.auto_paused = true THEN 'scheduled' ELSE 'user' END,
      jsonb_build_object(
        'account_name', NEW.account_name,
        'auto_paused_changed', OLD.auto_paused IS DISTINCT FROM NEW.auto_paused,
        'schedule_changed', (OLD.scheduled_pause_start IS DISTINCT FROM NEW.scheduled_pause_start OR 
                           OLD.scheduled_pause_end IS DISTINCT FROM NEW.scheduled_pause_end)
      )
    );
  END IF;

  -- Log schedule changes
  IF (OLD.scheduled_pause_start IS DISTINCT FROM NEW.scheduled_pause_start OR 
      OLD.scheduled_pause_end IS DISTINCT FROM NEW.scheduled_pause_end) AND
     NEW.scheduled_pause_start IS NOT NULL AND NEW.scheduled_pause_end IS NOT NULL THEN
    
    PERFORM log_pause_action(
      NEW.user_id,
      NEW.id,
      'schedule',
      NEW.scheduled_pause_reason,
      OLD.rounding_enabled,
      NEW.rounding_enabled,
      NEW.scheduled_pause_start,
      NEW.scheduled_pause_end,
      'user',
      jsonb_build_object(
        'account_name', NEW.account_name,
        'previous_start', OLD.scheduled_pause_start,
        'previous_end', OLD.scheduled_pause_end
      )
    );
  END IF;

  -- Log schedule cancellations
  IF OLD.scheduled_pause_start IS NOT NULL AND NEW.scheduled_pause_start IS NULL THEN
    PERFORM log_pause_action(
      NEW.user_id,
      NEW.id,
      'cancel_schedule',
      'Schedule cancelled',
      OLD.rounding_enabled,
      NEW.rounding_enabled,
      OLD.scheduled_pause_start,
      OLD.scheduled_pause_end,
      'user',
      jsonb_build_object(
        'account_name', NEW.account_name,
        'cancelled_start', OLD.scheduled_pause_start,
        'cancelled_end', OLD.scheduled_pause_end,
        'cancelled_reason', OLD.scheduled_pause_reason
      )
    );
  END IF;

  RETURN NEW;
END;
$$;

-- Create trigger on linked_accounts table
DROP TRIGGER IF EXISTS trigger_pause_history ON linked_accounts;
CREATE TRIGGER trigger_pause_history
  AFTER UPDATE ON linked_accounts
  FOR EACH ROW
  EXECUTE FUNCTION trigger_log_pause_action();

-- Grant permissions
GRANT EXECUTE ON FUNCTION log_pause_action(UUID, UUID, VARCHAR(20), TEXT, BOOLEAN, BOOLEAN, TIMESTAMP WITH TIME ZONE, TIMESTAMP WITH TIME ZONE, VARCHAR(20), JSONB) TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_pause_history(UUID, INTEGER, INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION get_account_pause_history(UUID, UUID, INTEGER) TO authenticated;

-- Add comments
COMMENT ON TABLE account_pause_history IS 'Tracks all pause/resume actions for linked accounts';
COMMENT ON FUNCTION log_pause_action(UUID, UUID, VARCHAR(20), TEXT, BOOLEAN, BOOLEAN, TIMESTAMP WITH TIME ZONE, TIMESTAMP WITH TIME ZONE, VARCHAR(20), JSONB) IS 'Logs a pause/resume action to the history table';
COMMENT ON FUNCTION get_user_pause_history(UUID, INTEGER, INTEGER) IS 'Gets pause history for a user with pagination';
COMMENT ON FUNCTION get_account_pause_history(UUID, UUID, INTEGER) IS 'Gets pause history for a specific account';
