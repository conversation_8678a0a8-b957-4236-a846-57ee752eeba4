-- User engagement features for pause/resume system
-- This includes smart suggestions, re-engagement prompts, and impact tracking

-- Create user engagement metrics table
CREATE TABLE IF NOT EXISTS user_engagement_metrics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  metric_type VARCHAR(50) NOT NULL, -- 'pause_suggestion', 'reengagement_prompt', 'impact_milestone'
  metric_value NUMERIC(10,2), -- Monetary value or score
  context_data JSONB, -- Additional context about the metric
  triggered_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  action_taken VARCHAR(50), -- 'accepted', 'dismissed', 'ignored'
  action_taken_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create smart suggestions table
CREATE TABLE IF NOT EXISTS smart_suggestions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  suggestion_type VARCHAR(50) NOT NULL, -- 'pause_for_vacation', 'pause_for_budget', 'resume_reminder', 'optimize_donations'
  title TEXT NOT NULL,
  description TEXT,
  confidence_score INTEGER CHECK (confidence_score >= 0 AND confidence_score <= 100),
  reasoning JSONB, -- Why this suggestion was made
  suggested_action JSONB, -- What action to take
  priority VARCHAR(20) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
  expires_at TIMESTAMP WITH TIME ZONE,
  status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'accepted', 'dismissed', 'expired')),
  status_updated_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create re-engagement campaigns table
CREATE TABLE IF NOT EXISTS reengagement_campaigns (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  campaign_type VARCHAR(50) NOT NULL, -- 'long_pause_reminder', 'impact_showcase', 'feature_highlight'
  title TEXT NOT NULL,
  message TEXT,
  call_to_action TEXT,
  personalization_data JSONB, -- User-specific data for personalization
  delivery_channel VARCHAR(30) DEFAULT 'in_app' CHECK (delivery_channel IN ('in_app', 'email', 'push')),
  scheduled_for TIMESTAMP WITH TIME ZONE,
  delivered_at TIMESTAMP WITH TIME ZONE,
  opened_at TIMESTAMP WITH TIME ZONE,
  clicked_at TIMESTAMP WITH TIME ZONE,
  status VARCHAR(20) DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'delivered', 'opened', 'clicked', 'failed')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create impact tracking table
CREATE TABLE IF NOT EXISTS impact_tracking (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  period_start TIMESTAMP WITH TIME ZONE NOT NULL,
  period_end TIMESTAMP WITH TIME ZONE NOT NULL,
  total_transactions INTEGER DEFAULT 0,
  active_transactions INTEGER DEFAULT 0, -- Transactions from active accounts
  paused_transactions INTEGER DEFAULT 0, -- Transactions from paused accounts
  total_round_ups NUMERIC(10,2) DEFAULT 0,
  donated_amount NUMERIC(10,2) DEFAULT 0, -- Amount actually donated
  saved_amount NUMERIC(10,2) DEFAULT 0, -- Amount saved due to paused accounts
  potential_donations NUMERIC(10,2) DEFAULT 0, -- What could have been donated if all active
  efficiency_score NUMERIC(5,2) DEFAULT 0, -- Percentage of potential donations achieved
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes
CREATE INDEX IF NOT EXISTS idx_user_engagement_metrics_user_id ON user_engagement_metrics(user_id);
CREATE INDEX IF NOT EXISTS idx_user_engagement_metrics_metric_type ON user_engagement_metrics(metric_type);
CREATE INDEX IF NOT EXISTS idx_user_engagement_metrics_triggered_at ON user_engagement_metrics(triggered_at DESC);

CREATE INDEX IF NOT EXISTS idx_smart_suggestions_user_id ON smart_suggestions(user_id);
CREATE INDEX IF NOT EXISTS idx_smart_suggestions_suggestion_type ON smart_suggestions(suggestion_type);
CREATE INDEX IF NOT EXISTS idx_smart_suggestions_status ON smart_suggestions(status);
CREATE INDEX IF NOT EXISTS idx_smart_suggestions_priority ON smart_suggestions(priority);
CREATE INDEX IF NOT EXISTS idx_smart_suggestions_expires_at ON smart_suggestions(expires_at);

CREATE INDEX IF NOT EXISTS idx_reengagement_campaigns_user_id ON reengagement_campaigns(user_id);
CREATE INDEX IF NOT EXISTS idx_reengagement_campaigns_campaign_type ON reengagement_campaigns(campaign_type);
CREATE INDEX IF NOT EXISTS idx_reengagement_campaigns_status ON reengagement_campaigns(status);
CREATE INDEX IF NOT EXISTS idx_reengagement_campaigns_scheduled_for ON reengagement_campaigns(scheduled_for);

CREATE INDEX IF NOT EXISTS idx_impact_tracking_user_id ON impact_tracking(user_id);
CREATE INDEX IF NOT EXISTS idx_impact_tracking_period_start ON impact_tracking(period_start);
CREATE INDEX IF NOT EXISTS idx_impact_tracking_period_end ON impact_tracking(period_end);

-- Enable RLS
ALTER TABLE user_engagement_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE smart_suggestions ENABLE ROW LEVEL SECURITY;
ALTER TABLE reengagement_campaigns ENABLE ROW LEVEL SECURITY;
ALTER TABLE impact_tracking ENABLE ROW LEVEL SECURITY;

-- RLS policies
CREATE POLICY "Users can view their own engagement metrics" ON user_engagement_metrics
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "System can manage engagement metrics" ON user_engagement_metrics
  FOR ALL USING (true);

CREATE POLICY "Users can view their own suggestions" ON smart_suggestions
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own suggestions" ON smart_suggestions
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "System can manage suggestions" ON smart_suggestions
  FOR ALL USING (true);

CREATE POLICY "Users can view their own campaigns" ON reengagement_campaigns
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "System can manage campaigns" ON reengagement_campaigns
  FOR ALL USING (true);

CREATE POLICY "Users can view their own impact tracking" ON impact_tracking
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "System can manage impact tracking" ON impact_tracking
  FOR ALL USING (true);

-- Function to generate smart pause suggestions
CREATE OR REPLACE FUNCTION generate_smart_suggestions(p_user_id UUID)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  user_patterns RECORD;
  account_activity RECORD;
  suggestion_record RECORD;
BEGIN
  -- Get user behavior patterns
  SELECT 
    COUNT(*) FILTER (WHERE operation_type IN ('pause', 'resume')) as pause_resume_count,
    COUNT(*) FILTER (WHERE operation_type = 'pause') as pause_count,
    COUNT(*) FILTER (WHERE operation_subtype = 'bulk') as bulk_operations,
    AVG(EXTRACT(EPOCH FROM (NOW() - created_at))/86400) as avg_days_since_activity
  INTO user_patterns
  FROM operation_metrics
  WHERE user_id = p_user_id
    AND created_at >= NOW() - INTERVAL '30 days';

  -- Get account activity data
  SELECT 
    COUNT(*) as total_accounts,
    COUNT(*) FILTER (WHERE rounding_enabled = true) as active_accounts,
    COUNT(*) FILTER (WHERE rounding_enabled = false) as paused_accounts,
    AVG(CASE WHEN rounding_enabled = false THEN EXTRACT(EPOCH FROM (NOW() - updated_at))/86400 END) as avg_pause_days
  INTO account_activity
  FROM linked_accounts
  WHERE user_id = p_user_id
    AND deleted_at IS NULL;

  -- Clear expired suggestions
  UPDATE smart_suggestions 
  SET status = 'expired', status_updated_at = NOW()
  WHERE user_id = p_user_id 
    AND status = 'active' 
    AND expires_at < NOW();

  -- Suggestion 1: Vacation pause reminder (if user has upcoming scheduled pauses)
  IF EXISTS (
    SELECT 1 FROM linked_accounts 
    WHERE user_id = p_user_id 
      AND scheduled_pause_start IS NOT NULL 
      AND scheduled_pause_start <= NOW() + INTERVAL '7 days'
      AND scheduled_pause_start > NOW()
  ) THEN
    INSERT INTO smart_suggestions (
      user_id, suggestion_type, title, description, confidence_score, 
      reasoning, suggested_action, priority, expires_at
    )
    SELECT 
      p_user_id,
      'pause_for_vacation',
      'Upcoming Vacation Pause Reminder',
      'You have scheduled pauses coming up. Would you like to review or modify them?',
      85,
      jsonb_build_object('trigger', 'scheduled_pause_approaching'),
      jsonb_build_object('action', 'review_scheduled_pauses'),
      'medium',
      NOW() + INTERVAL '7 days'
    WHERE NOT EXISTS (
      SELECT 1 FROM smart_suggestions 
      WHERE user_id = p_user_id 
        AND suggestion_type = 'pause_for_vacation' 
        AND status = 'active'
    );
  END IF;

  -- Suggestion 2: Resume reminder for long-paused accounts
  IF account_activity.avg_pause_days > 30 AND account_activity.paused_accounts > 0 THEN
    INSERT INTO smart_suggestions (
      user_id, suggestion_type, title, description, confidence_score,
      reasoning, suggested_action, priority, expires_at
    )
    SELECT 
      p_user_id,
      'resume_reminder',
      'Consider Resuming Paused Accounts',
      format('You have %s account(s) paused for an average of %s days. Ready to resume donations?', 
             account_activity.paused_accounts, ROUND(account_activity.avg_pause_days)),
      70,
      jsonb_build_object(
        'trigger', 'long_pause_detected',
        'paused_accounts', account_activity.paused_accounts,
        'avg_pause_days', account_activity.avg_pause_days
      ),
      jsonb_build_object('action', 'review_paused_accounts'),
      'medium',
      NOW() + INTERVAL '14 days'
    WHERE NOT EXISTS (
      SELECT 1 FROM smart_suggestions 
      WHERE user_id = p_user_id 
        AND suggestion_type = 'resume_reminder' 
        AND status = 'active'
    );
  END IF;

  -- Suggestion 3: Optimize donations (if user has mixed active/paused accounts)
  IF account_activity.active_accounts > 0 AND account_activity.paused_accounts > 0 THEN
    INSERT INTO smart_suggestions (
      user_id, suggestion_type, title, description, confidence_score,
      reasoning, suggested_action, priority, expires_at
    )
    SELECT 
      p_user_id,
      'optimize_donations',
      'Optimize Your Donation Strategy',
      format('You have %s active and %s paused accounts. Review your setup to maximize impact.',
             account_activity.active_accounts, account_activity.paused_accounts),
      60,
      jsonb_build_object(
        'trigger', 'mixed_account_states',
        'active_accounts', account_activity.active_accounts,
        'paused_accounts', account_activity.paused_accounts
      ),
      jsonb_build_object('action', 'review_all_accounts'),
      'low',
      NOW() + INTERVAL '30 days'
    WHERE NOT EXISTS (
      SELECT 1 FROM smart_suggestions 
      WHERE user_id = p_user_id 
        AND suggestion_type = 'optimize_donations' 
        AND status = 'active'
    );
  END IF;

  -- Suggestion 4: Budget pause (if user has been very active with pausing recently)
  IF user_patterns.pause_count > 5 AND user_patterns.avg_days_since_activity < 7 THEN
    INSERT INTO smart_suggestions (
      user_id, suggestion_type, title, description, confidence_score,
      reasoning, suggested_action, priority, expires_at
    )
    SELECT 
      p_user_id,
      'pause_for_budget',
      'Consider a Temporary Budget Break',
      'You''ve been pausing accounts frequently. Would you like to set up a scheduled break?',
      75,
      jsonb_build_object(
        'trigger', 'frequent_pausing',
        'recent_pause_count', user_patterns.pause_count
      ),
      jsonb_build_object('action', 'schedule_budget_break'),
      'medium',
      NOW() + INTERVAL '7 days'
    WHERE NOT EXISTS (
      SELECT 1 FROM smart_suggestions 
      WHERE user_id = p_user_id 
        AND suggestion_type = 'pause_for_budget' 
        AND status = 'active'
    );
  END IF;
END;
$$;

-- Function to create re-engagement campaigns
CREATE OR REPLACE FUNCTION create_reengagement_campaign(
  p_user_id UUID,
  p_campaign_type VARCHAR(50),
  p_personalization_data JSONB DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  campaign_id UUID;
  campaign_title TEXT;
  campaign_message TEXT;
  campaign_cta TEXT;
  schedule_delay INTERVAL;
BEGIN
  -- Determine campaign content based on type
  CASE p_campaign_type
    WHEN 'long_pause_reminder' THEN
      campaign_title := 'Your donations are paused - ready to resume?';
      campaign_message := 'You''ve had accounts paused for a while. Every small donation makes a difference!';
      campaign_cta := 'Review Paused Accounts';
      schedule_delay := INTERVAL '1 day';
    
    WHEN 'impact_showcase' THEN
      campaign_title := 'See your donation impact!';
      campaign_message := 'Check out how much you''ve donated and the difference you''ve made.';
      campaign_cta := 'View Impact Report';
      schedule_delay := INTERVAL '3 days';
    
    WHEN 'feature_highlight' THEN
      campaign_title := 'New features to help you donate smarter';
      campaign_message := 'Discover new ways to manage your round-up donations effectively.';
      campaign_cta := 'Explore Features';
      schedule_delay := INTERVAL '7 days';
    
    ELSE
      RAISE EXCEPTION 'Unknown campaign type: %', p_campaign_type;
  END CASE;

  -- Create the campaign
  INSERT INTO reengagement_campaigns (
    user_id,
    campaign_type,
    title,
    message,
    call_to_action,
    personalization_data,
    scheduled_for
  ) VALUES (
    p_user_id,
    p_campaign_type,
    campaign_title,
    campaign_message,
    campaign_cta,
    p_personalization_data,
    NOW() + schedule_delay
  ) RETURNING id INTO campaign_id;

  RETURN campaign_id;
END;
$$;

-- Function to calculate impact metrics
CREATE OR REPLACE FUNCTION calculate_impact_metrics(
  p_user_id UUID,
  p_period_start TIMESTAMP WITH TIME ZONE,
  p_period_end TIMESTAMP WITH TIME ZONE
)
RETURNS TABLE (
  total_transactions INTEGER,
  donated_amount NUMERIC(10,2),
  saved_amount NUMERIC(10,2),
  efficiency_score NUMERIC(5,2)
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  total_txns INTEGER := 0;
  donated_amt NUMERIC(10,2) := 0;
  saved_amt NUMERIC(10,2) := 0;
  efficiency NUMERIC(5,2) := 0;
BEGIN
  -- This is a simplified calculation - in reality would integrate with transaction data
  -- For now, we'll use mock calculations based on account states
  
  -- Get transaction counts (mock data)
  SELECT 
    COALESCE(COUNT(*), 0)
  INTO total_txns
  FROM linked_accounts la
  WHERE la.user_id = p_user_id
    AND la.deleted_at IS NULL;
  
  -- Estimate donated amount (mock calculation)
  SELECT 
    COALESCE(COUNT(*) FILTER (WHERE rounding_enabled = true) * 2.50, 0)
  INTO donated_amt
  FROM linked_accounts
  WHERE user_id = p_user_id
    AND deleted_at IS NULL;
  
  -- Estimate saved amount (mock calculation)
  SELECT 
    COALESCE(COUNT(*) FILTER (WHERE rounding_enabled = false) * 2.50, 0)
  INTO saved_amt
  FROM linked_accounts
  WHERE user_id = p_user_id
    AND deleted_at IS NULL;
  
  -- Calculate efficiency score
  IF (donated_amt + saved_amt) > 0 THEN
    efficiency := (donated_amt / (donated_amt + saved_amt)) * 100;
  END IF;

  RETURN QUERY SELECT total_txns, donated_amt, saved_amt, efficiency;
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION generate_smart_suggestions(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION create_reengagement_campaign(UUID, VARCHAR(50), JSONB) TO authenticated;
GRANT EXECUTE ON FUNCTION calculate_impact_metrics(UUID, TIMESTAMP WITH TIME ZONE, TIMESTAMP WITH TIME ZONE) TO authenticated;

-- Add unique constraints
ALTER TABLE smart_suggestions ADD CONSTRAINT unique_active_suggestion UNIQUE (user_id, suggestion_type, status) DEFERRABLE INITIALLY DEFERRED;

-- Add comments
COMMENT ON TABLE user_engagement_metrics IS 'Tracks user engagement metrics and actions taken';
COMMENT ON TABLE smart_suggestions IS 'AI-generated suggestions for optimizing user donation behavior';
COMMENT ON TABLE reengagement_campaigns IS 'Automated campaigns to re-engage inactive users';
COMMENT ON TABLE impact_tracking IS 'Tracks user donation impact and savings over time';

COMMENT ON FUNCTION generate_smart_suggestions(UUID) IS 'Generates personalized suggestions for a user based on their behavior';
COMMENT ON FUNCTION create_reengagement_campaign(UUID, VARCHAR(50), JSONB) IS 'Creates a re-engagement campaign for a user';
COMMENT ON FUNCTION calculate_impact_metrics(UUID, TIMESTAMP WITH TIME ZONE, TIMESTAMP WITH TIME ZONE) IS 'Calculates donation impact metrics for a user over a time period';
