-- Add rounding_enabled column for pause/resume functionality
ALTER TABLE linked_accounts ADD COLUMN IF NOT EXISTS rounding_enabled BOOLEAN DEFAULT true;

-- Add soft delete functionality while preserving records for tax purposes
ALTER TABLE linked_accounts ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP WITH TIME ZONE NULL;
ALTER TABLE linked_accounts ADD COLUMN IF NOT EXISTS deletion_reason TEXT NULL;

-- Update the existing query to only show non-deleted accounts
-- The is_active field will now be used for pause/resume
-- The deleted_at field will be used for soft deletion

-- Add index for better performance
CREATE INDEX IF NOT EXISTS idx_linked_accounts_active_not_deleted 
ON linked_accounts (user_id, is_active) 
WHERE deleted_at IS NULL;

-- Add index for tax reporting queries
CREATE INDEX IF NOT EXISTS idx_linked_accounts_deleted 
ON linked_accounts (user_id, deleted_at) 
WHERE deleted_at IS NOT NULL;