-- Charity-Giver Connection System Functions
-- Phase 1: Core functions for donation processing and analytics

-- =====================================================
-- 1. DONATION PROCESSING FUNCTIONS
-- =====================================================

-- Function to process round-up donations and distribute to charities
CREATE OR REPLACE FUNCTION process_round_up_donations(
  p_user_id UUID,
  p_transaction_id INTEGER,
  p_round_up_amount NUMERIC(10,2)
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_user_follows RECORD;
  v_charity_allocation JSONB := '[]'::JSONB;
  v_allocation_item JSONB;
  v_total_percentage NUMERIC := 0;
  v_remaining_amount NUMERIC := p_round_up_amount;
  v_distribution_id UUID;
  v_donation_id INTEGER;
  v_charity_amount NUMERIC;
  v_batch_id UUID;
BEGIN
  -- Validate input
  IF p_round_up_amount <= 0 THEN
    RETURN jsonb_build_object('success', false, 'error', 'Invalid round-up amount');
  END IF;

  -- Get user's charity follows with allocation percentages
  SELECT SUM(allocation_percentage) INTO v_total_percentage
  FROM user_charity_follows 
  WHERE user_id = p_user_id;

  -- If user has no charity follows or total percentage is 0, skip processing
  IF v_total_percentage IS NULL OR v_total_percentage = 0 THEN
    RETURN jsonb_build_object(
      'success', false, 
      'error', 'User has no charity allocations set up'
    );
  END IF;

  -- Normalize percentages if they don't add up to 100%
  IF v_total_percentage != 100 THEN
    -- This will proportionally adjust allocations to total 100%
    NULL; -- We'll handle this in the loop below
  END IF;

  -- Get or create today's donation batch
  INSERT INTO donation_batches (batch_date, total_amount, total_transactions, total_users)
  VALUES (CURRENT_DATE, 0, 0, 0)
  ON CONFLICT (batch_date) DO NOTHING;
  
  SELECT id INTO v_batch_id FROM donation_batches WHERE batch_date = CURRENT_DATE;

  -- Create distribution record
  INSERT INTO donation_distributions (
    user_id, 
    transaction_id, 
    batch_id,
    total_round_up_amount,
    status
  ) VALUES (
    p_user_id, 
    p_transaction_id, 
    v_batch_id,
    p_round_up_amount,
    'pending'
  ) RETURNING id INTO v_distribution_id;

  -- Process each charity allocation
  FOR v_user_follows IN 
    SELECT charity_id, allocation_percentage
    FROM user_charity_follows 
    WHERE user_id = p_user_id 
    AND allocation_percentage > 0
    ORDER BY charity_id
  LOOP
    -- Calculate amount for this charity (normalize percentage)
    v_charity_amount := ROUND(
      (p_round_up_amount * v_user_follows.allocation_percentage / v_total_percentage), 
      2
    );
    
    -- Ensure we don't exceed the total amount
    IF v_charity_amount > v_remaining_amount THEN
      v_charity_amount := v_remaining_amount;
    END IF;
    
    -- Skip if amount is too small (less than 1 cent)
    IF v_charity_amount < 0.01 THEN
      CONTINUE;
    END IF;

    -- Create donation record
    INSERT INTO donations (
      user_id,
      charity_id,
      amount,
      net_amount,
      transaction_ids,
      status,
      donation_date,
      batch_id,
      payment_method
    ) VALUES (
      p_user_id,
      v_user_follows.charity_id,
      v_charity_amount,
      v_charity_amount, -- No processing fees for now
      ARRAY[p_transaction_id],
      'pending',
      NOW(),
      v_batch_id,
      'round_up'
    ) RETURNING id INTO v_donation_id;

    -- Add to allocation tracking
    v_allocation_item := jsonb_build_object(
      'charity_id', v_user_follows.charity_id,
      'percentage', v_user_follows.allocation_percentage,
      'amount', v_charity_amount,
      'donation_id', v_donation_id
    );
    v_charity_allocation := v_charity_allocation || v_allocation_item;

    -- Update remaining amount
    v_remaining_amount := v_remaining_amount - v_charity_amount;
  END LOOP;

  -- Update distribution record with allocation details
  UPDATE donation_distributions 
  SET 
    charity_allocations = v_charity_allocation,
    status = 'distributed',
    processed_at = NOW()
  WHERE id = v_distribution_id;

  -- Update batch totals
  UPDATE donation_batches 
  SET 
    total_amount = total_amount + p_round_up_amount,
    total_transactions = total_transactions + 1,
    total_users = (
      SELECT COUNT(DISTINCT user_id) 
      FROM donation_distributions 
      WHERE batch_id = v_batch_id
    )
  WHERE id = v_batch_id;

  -- Update charity totals
  FOR v_user_follows IN 
    SELECT charity_id, SUM(amount) as total_amount
    FROM donations 
    WHERE batch_id = v_batch_id 
    AND charity_id IS NOT NULL
    GROUP BY charity_id
  LOOP
    UPDATE charities 
    SET 
      total_raised = COALESCE(total_raised, 0) + v_user_follows.total_amount,
      updated_at = NOW()
    WHERE id = v_user_follows.charity_id;
  END LOOP;

  RETURN jsonb_build_object(
    'success', true,
    'distribution_id', v_distribution_id,
    'total_amount', p_round_up_amount,
    'charity_allocations', v_charity_allocation,
    'batch_id', v_batch_id
  );
END;
$$;

-- Function to complete donation batch processing
CREATE OR REPLACE FUNCTION complete_donation_batch(p_batch_date DATE DEFAULT CURRENT_DATE)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_batch_id UUID;
  v_total_amount NUMERIC;
  v_total_donations INTEGER;
  v_affected_charities INTEGER;
BEGIN
  -- Get batch ID
  SELECT id, total_amount INTO v_batch_id, v_total_amount
  FROM donation_batches 
  WHERE batch_date = p_batch_date;

  IF v_batch_id IS NULL THEN
    RETURN jsonb_build_object('success', false, 'error', 'Batch not found');
  END IF;

  -- Update all pending donations in this batch to completed
  UPDATE donations 
  SET 
    status = 'completed',
    updated_at = NOW()
  WHERE batch_id = v_batch_id 
  AND status = 'pending';

  GET DIAGNOSTICS v_total_donations = ROW_COUNT;

  -- Count affected charities
  SELECT COUNT(DISTINCT charity_id) INTO v_affected_charities
  FROM donations 
  WHERE batch_id = v_batch_id;

  -- Mark batch as completed
  UPDATE donation_batches 
  SET 
    processing_status = 'completed',
    processed_at = NOW()
  WHERE id = v_batch_id;

  RETURN jsonb_build_object(
    'success', true,
    'batch_id', v_batch_id,
    'total_amount', v_total_amount,
    'total_donations', v_total_donations,
    'affected_charities', v_affected_charities
  );
END;
$$;

-- =====================================================
-- 2. ANALYTICS GENERATION FUNCTIONS
-- =====================================================

-- Function to generate daily analytics for charities (privacy-safe)
CREATE OR REPLACE FUNCTION generate_charity_daily_analytics(
  p_analytics_date DATE DEFAULT CURRENT_DATE
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_charity RECORD;
  v_analytics_count INTEGER := 0;
BEGIN
  -- Generate analytics for each charity that received donations
  FOR v_charity IN 
    SELECT DISTINCT charity_id 
    FROM donations 
    WHERE DATE(donation_date) = p_analytics_date
    AND status IN ('completed', 'pending')
  LOOP
    -- Insert or update daily analytics
    INSERT INTO charity_daily_analytics (
      charity_id,
      analytics_date,
      total_donations_count,
      total_donations_amount,
      average_donation_amount,
      active_supporters_count,
      new_supporters_count,
      returning_supporters_count,
      round_up_donations_count,
      round_up_donations_amount,
      direct_donations_count,
      direct_donations_amount
    )
    SELECT 
      v_charity.charity_id,
      p_analytics_date,
      COUNT(*) as total_donations_count,
      SUM(amount) as total_donations_amount,
      AVG(amount) as average_donation_amount,
      COUNT(DISTINCT user_id) as active_supporters_count,
      -- New supporters: first donation ever to this charity
      COUNT(DISTINCT CASE 
        WHEN NOT EXISTS (
          SELECT 1 FROM donations d2 
          WHERE d2.user_id = d.user_id 
          AND d2.charity_id = v_charity.charity_id 
          AND DATE(d2.donation_date) < p_analytics_date
        ) THEN user_id 
      END) as new_supporters_count,
      -- Returning supporters: have donated before
      COUNT(DISTINCT CASE 
        WHEN EXISTS (
          SELECT 1 FROM donations d2 
          WHERE d2.user_id = d.user_id 
          AND d2.charity_id = v_charity.charity_id 
          AND DATE(d2.donation_date) < p_analytics_date
        ) THEN user_id 
      END) as returning_supporters_count,
      COUNT(CASE WHEN payment_method = 'round_up' THEN 1 END) as round_up_donations_count,
      SUM(CASE WHEN payment_method = 'round_up' THEN amount ELSE 0 END) as round_up_donations_amount,
      COUNT(CASE WHEN payment_method != 'round_up' THEN 1 END) as direct_donations_count,
      SUM(CASE WHEN payment_method != 'round_up' THEN amount ELSE 0 END) as direct_donations_amount
    FROM donations d
    WHERE charity_id = v_charity.charity_id
    AND DATE(donation_date) = p_analytics_date
    AND status IN ('completed', 'pending')
    GROUP BY charity_id
    
    ON CONFLICT (charity_id, analytics_date) 
    DO UPDATE SET
      total_donations_count = EXCLUDED.total_donations_count,
      total_donations_amount = EXCLUDED.total_donations_amount,
      average_donation_amount = EXCLUDED.average_donation_amount,
      active_supporters_count = EXCLUDED.active_supporters_count,
      new_supporters_count = EXCLUDED.new_supporters_count,
      returning_supporters_count = EXCLUDED.returning_supporters_count,
      round_up_donations_count = EXCLUDED.round_up_donations_count,
      round_up_donations_amount = EXCLUDED.round_up_donations_amount,
      direct_donations_count = EXCLUDED.direct_donations_count,
      direct_donations_amount = EXCLUDED.direct_donations_amount,
      updated_at = NOW();

    v_analytics_count := v_analytics_count + 1;
  END LOOP;

  RETURN jsonb_build_object(
    'success', true,
    'analytics_date', p_analytics_date,
    'charities_processed', v_analytics_count
  );
END;
$$;

-- Function to generate monthly analytics summary
CREATE OR REPLACE FUNCTION generate_charity_monthly_analytics(
  p_year INTEGER DEFAULT EXTRACT(YEAR FROM CURRENT_DATE),
  p_month INTEGER DEFAULT EXTRACT(MONTH FROM CURRENT_DATE)
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_charity RECORD;
  v_analytics_count INTEGER := 0;
  v_start_date DATE;
  v_end_date DATE;
BEGIN
  -- Calculate month boundaries
  v_start_date := DATE(p_year || '-' || p_month || '-01');
  v_end_date := (v_start_date + INTERVAL '1 month' - INTERVAL '1 day')::DATE;

  -- Generate monthly analytics for each charity
  FOR v_charity IN 
    SELECT DISTINCT charity_id 
    FROM charity_daily_analytics 
    WHERE analytics_date >= v_start_date 
    AND analytics_date <= v_end_date
  LOOP
    INSERT INTO charity_monthly_analytics (
      charity_id,
      year,
      month,
      total_donations_count,
      total_donations_amount,
      average_daily_donations,
      peak_donation_day,
      peak_donation_amount,
      total_supporters_count,
      new_supporters_count
    )
    SELECT 
      v_charity.charity_id,
      p_year,
      p_month,
      SUM(total_donations_count) as total_donations_count,
      SUM(total_donations_amount) as total_donations_amount,
      AVG(total_donations_amount) as average_daily_donations,
      (SELECT analytics_date FROM charity_daily_analytics 
       WHERE charity_id = v_charity.charity_id 
       AND analytics_date >= v_start_date 
       AND analytics_date <= v_end_date
       ORDER BY total_donations_amount DESC 
       LIMIT 1) as peak_donation_day,
      MAX(total_donations_amount) as peak_donation_amount,
      -- Total unique supporters for the month
      (SELECT COUNT(DISTINCT user_id) 
       FROM donations 
       WHERE charity_id = v_charity.charity_id 
       AND DATE(donation_date) >= v_start_date 
       AND DATE(donation_date) <= v_end_date
       AND status IN ('completed', 'pending')) as total_supporters_count,
      SUM(new_supporters_count) as new_supporters_count
    FROM charity_daily_analytics
    WHERE charity_id = v_charity.charity_id
    AND analytics_date >= v_start_date 
    AND analytics_date <= v_end_date
    GROUP BY charity_id
    
    ON CONFLICT (charity_id, year, month) 
    DO UPDATE SET
      total_donations_count = EXCLUDED.total_donations_count,
      total_donations_amount = EXCLUDED.total_donations_amount,
      average_daily_donations = EXCLUDED.average_daily_donations,
      peak_donation_day = EXCLUDED.peak_donation_day,
      peak_donation_amount = EXCLUDED.peak_donation_amount,
      total_supporters_count = EXCLUDED.total_supporters_count,
      new_supporters_count = EXCLUDED.new_supporters_count,
      updated_at = NOW();

    v_analytics_count := v_analytics_count + 1;
  END LOOP;

  RETURN jsonb_build_object(
    'success', true,
    'year', p_year,
    'month', p_month,
    'charities_processed', v_analytics_count
  );
END;
$$;

-- =====================================================
-- 3. CHARITY AUTHENTICATION FUNCTIONS
-- =====================================================

-- Function to create charity representative session
CREATE OR REPLACE FUNCTION create_charity_auth_session(
  p_representative_id UUID,
  p_ip_address INET DEFAULT NULL,
  p_user_agent TEXT DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_session_token TEXT;
  v_charity_id INTEGER;
  v_expires_at TIMESTAMP WITH TIME ZONE;
BEGIN
  -- Get charity ID for the representative
  SELECT charity_id INTO v_charity_id
  FROM charity_representatives 
  WHERE id = p_representative_id AND is_active = true;

  IF v_charity_id IS NULL THEN
    RETURN jsonb_build_object('success', false, 'error', 'Invalid or inactive representative');
  END IF;

  -- Generate session token
  v_session_token := encode(gen_random_bytes(32), 'base64');
  v_expires_at := NOW() + INTERVAL '24 hours';

  -- Create session
  INSERT INTO charity_auth_sessions (
    representative_id,
    charity_id,
    session_token,
    expires_at,
    ip_address,
    user_agent
  ) VALUES (
    p_representative_id,
    v_charity_id,
    v_session_token,
    v_expires_at,
    p_ip_address,
    p_user_agent
  );

  -- Update last login
  UPDATE charity_representatives 
  SET last_login_at = NOW() 
  WHERE id = p_representative_id;

  RETURN jsonb_build_object(
    'success', true,
    'session_token', v_session_token,
    'expires_at', v_expires_at,
    'charity_id', v_charity_id
  );
END;
$$;

-- Function to validate charity session
CREATE OR REPLACE FUNCTION validate_charity_session(p_session_token TEXT)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_session RECORD;
BEGIN
  SELECT 
    s.representative_id,
    s.charity_id,
    s.expires_at,
    r.email,
    r.full_name,
    r.role,
    c.name as charity_name
  INTO v_session
  FROM charity_auth_sessions s
  JOIN charity_representatives r ON s.representative_id = r.id
  JOIN charities c ON s.charity_id = c.id
  WHERE s.session_token = p_session_token
  AND s.expires_at > NOW()
  AND r.is_active = true;

  IF v_session.representative_id IS NULL THEN
    RETURN jsonb_build_object('success', false, 'error', 'Invalid or expired session');
  END IF;

  -- Update last accessed
  UPDATE charity_auth_sessions 
  SET last_accessed_at = NOW() 
  WHERE session_token = p_session_token;

  RETURN jsonb_build_object(
    'success', true,
    'representative_id', v_session.representative_id,
    'charity_id', v_session.charity_id,
    'email', v_session.email,
    'full_name', v_session.full_name,
    'role', v_session.role,
    'charity_name', v_session.charity_name
  );
END;
$$;
