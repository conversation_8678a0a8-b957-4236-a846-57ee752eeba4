-- Security and audit logging for pause/resume operations
-- This provides comprehensive security monitoring and audit trails

-- Create audit log table for all account changes
CREATE TABLE IF NOT EXISTS account_audit_log (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  account_id UUID NOT NULL REFERENCES linked_accounts(id) ON DELETE CASCADE,
  action VARCHAR(50) NOT NULL, -- 'pause', 'resume', 'schedule', 'cancel_schedule', 'delete', 'create'
  old_values JSONB, -- Previous state of the account
  new_values JSONB, -- New state of the account
  changed_fields TEXT[], -- Array of field names that changed
  reason TEXT, -- User-provided reason
  ip_address INET, -- Source IP address
  user_agent TEXT, -- Browser/client information
  session_id TEXT, -- Session identifier
  request_id TEXT, -- Unique request identifier
  api_endpoint TEXT, -- API endpoint used
  http_method VARCHAR(10), -- HTTP method (GET, POST, etc.)
  security_context JSONB, -- Additional security context
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create rate limiting table
CREATE TABLE IF NOT EXISTS rate_limits (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  operation_type VARCHAR(50) NOT NULL,
  window_start TIMESTAMP WITH TIME ZONE NOT NULL,
  request_count INTEGER DEFAULT 1,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create security alerts table
CREATE TABLE IF NOT EXISTS security_alerts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  alert_type VARCHAR(50) NOT NULL, -- 'rate_limit_exceeded', 'suspicious_activity', 'unauthorized_access'
  severity VARCHAR(20) NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
  title TEXT NOT NULL,
  description TEXT,
  source_ip INET,
  user_agent TEXT,
  request_details JSONB,
  resolved BOOLEAN DEFAULT false,
  resolved_at TIMESTAMP WITH TIME ZONE,
  resolved_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create suspicious activity tracking
CREATE TABLE IF NOT EXISTS suspicious_activity (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  activity_type VARCHAR(50) NOT NULL,
  risk_score INTEGER CHECK (risk_score >= 0 AND risk_score <= 100),
  indicators JSONB, -- Array of risk indicators
  source_ip INET,
  user_agent TEXT,
  session_id TEXT,
  additional_context JSONB,
  investigated BOOLEAN DEFAULT false,
  investigated_at TIMESTAMP WITH TIME ZONE,
  investigated_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for efficient querying
CREATE INDEX IF NOT EXISTS idx_account_audit_log_user_id ON account_audit_log(user_id);
CREATE INDEX IF NOT EXISTS idx_account_audit_log_account_id ON account_audit_log(account_id);
CREATE INDEX IF NOT EXISTS idx_account_audit_log_action ON account_audit_log(action);
CREATE INDEX IF NOT EXISTS idx_account_audit_log_created_at ON account_audit_log(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_rate_limits_user_id ON rate_limits(user_id);
CREATE INDEX IF NOT EXISTS idx_rate_limits_operation_type ON rate_limits(operation_type);
CREATE INDEX IF NOT EXISTS idx_rate_limits_window_start ON rate_limits(window_start);

CREATE INDEX IF NOT EXISTS idx_security_alerts_user_id ON security_alerts(user_id);
CREATE INDEX IF NOT EXISTS idx_security_alerts_alert_type ON security_alerts(alert_type);
CREATE INDEX IF NOT EXISTS idx_security_alerts_severity ON security_alerts(severity);
CREATE INDEX IF NOT EXISTS idx_security_alerts_resolved ON security_alerts(resolved);
CREATE INDEX IF NOT EXISTS idx_security_alerts_created_at ON security_alerts(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_suspicious_activity_user_id ON suspicious_activity(user_id);
CREATE INDEX IF NOT EXISTS idx_suspicious_activity_activity_type ON suspicious_activity(activity_type);
CREATE INDEX IF NOT EXISTS idx_suspicious_activity_risk_score ON suspicious_activity(risk_score);
CREATE INDEX IF NOT EXISTS idx_suspicious_activity_investigated ON suspicious_activity(investigated);

-- Enable RLS
ALTER TABLE account_audit_log ENABLE ROW LEVEL SECURITY;
ALTER TABLE rate_limits ENABLE ROW LEVEL SECURITY;
ALTER TABLE security_alerts ENABLE ROW LEVEL SECURITY;
ALTER TABLE suspicious_activity ENABLE ROW LEVEL SECURITY;

-- RLS policies for account_audit_log
CREATE POLICY "Users can view their own audit logs" ON account_audit_log
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "System can insert audit logs" ON account_audit_log
  FOR INSERT WITH CHECK (true);

-- RLS policies for rate_limits
CREATE POLICY "Users can view their own rate limits" ON rate_limits
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "System can manage rate limits" ON rate_limits
  FOR ALL USING (true);

-- RLS policies for security_alerts (admin access)
CREATE POLICY "Admins can view all security alerts" ON security_alerts
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.uid() = id 
      AND raw_user_meta_data->>'role' = 'admin'
    )
  );

CREATE POLICY "System can manage security alerts" ON security_alerts
  FOR ALL USING (true);

-- RLS policies for suspicious_activity (admin access)
CREATE POLICY "Admins can view all suspicious activity" ON suspicious_activity
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.uid() = id 
      AND raw_user_meta_data->>'role' = 'admin'
    )
  );

CREATE POLICY "System can manage suspicious activity" ON suspicious_activity
  FOR ALL USING (true);

-- Function to log account changes with full audit trail
CREATE OR REPLACE FUNCTION log_account_audit(
  p_user_id UUID,
  p_account_id UUID,
  p_action VARCHAR(50),
  p_old_values JSONB DEFAULT NULL,
  p_new_values JSONB DEFAULT NULL,
  p_changed_fields TEXT[] DEFAULT NULL,
  p_reason TEXT DEFAULT NULL,
  p_ip_address INET DEFAULT NULL,
  p_user_agent TEXT DEFAULT NULL,
  p_session_id TEXT DEFAULT NULL,
  p_request_id TEXT DEFAULT NULL,
  p_api_endpoint TEXT DEFAULT NULL,
  p_http_method VARCHAR(10) DEFAULT NULL,
  p_security_context JSONB DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  audit_id UUID;
BEGIN
  INSERT INTO account_audit_log (
    user_id,
    account_id,
    action,
    old_values,
    new_values,
    changed_fields,
    reason,
    ip_address,
    user_agent,
    session_id,
    request_id,
    api_endpoint,
    http_method,
    security_context
  ) VALUES (
    p_user_id,
    p_account_id,
    p_action,
    p_old_values,
    p_new_values,
    p_changed_fields,
    p_reason,
    p_ip_address,
    p_user_agent,
    p_session_id,
    p_request_id,
    p_api_endpoint,
    p_http_method,
    p_security_context
  ) RETURNING id INTO audit_id;

  -- Check for suspicious activity
  PERFORM analyze_suspicious_activity(p_user_id, p_action, p_ip_address, p_user_agent);

  RETURN audit_id;
END;
$$;

-- Function to check and enforce rate limits
CREATE OR REPLACE FUNCTION check_rate_limit(
  p_user_id UUID,
  p_operation_type VARCHAR(50),
  p_limit INTEGER DEFAULT 10,
  p_window_minutes INTEGER DEFAULT 60
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  current_count INTEGER;
  window_start TIMESTAMP WITH TIME ZONE;
BEGIN
  window_start := NOW() - (p_window_minutes || ' minutes')::INTERVAL;
  
  -- Clean up old rate limit records
  DELETE FROM rate_limits 
  WHERE window_start < window_start;
  
  -- Get current count for this user and operation type
  SELECT COALESCE(SUM(request_count), 0) INTO current_count
  FROM rate_limits
  WHERE user_id = p_user_id
    AND operation_type = p_operation_type
    AND window_start >= window_start;
  
  -- Check if limit exceeded
  IF current_count >= p_limit THEN
    -- Log security alert
    INSERT INTO security_alerts (
      user_id, alert_type, severity, title, description
    ) VALUES (
      p_user_id,
      'rate_limit_exceeded',
      'medium',
      'Rate Limit Exceeded',
      'User exceeded rate limit for ' || p_operation_type || ' operations'
    );
    
    RETURN FALSE;
  END IF;
  
  -- Update or insert rate limit record
  INSERT INTO rate_limits (user_id, operation_type, window_start, request_count)
  VALUES (p_user_id, p_operation_type, DATE_TRUNC('minute', NOW()), 1)
  ON CONFLICT (user_id, operation_type, window_start)
  DO UPDATE SET 
    request_count = rate_limits.request_count + 1,
    updated_at = NOW();
  
  RETURN TRUE;
END;
$$;

-- Function to analyze suspicious activity
CREATE OR REPLACE FUNCTION analyze_suspicious_activity(
  p_user_id UUID,
  p_action VARCHAR(50),
  p_ip_address INET DEFAULT NULL,
  p_user_agent TEXT DEFAULT NULL
)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  risk_score INTEGER := 0;
  indicators JSONB := '[]'::JSONB;
  recent_actions INTEGER;
  unique_ips INTEGER;
  rapid_changes INTEGER;
BEGIN
  -- Check for rapid successive actions (within 1 minute)
  SELECT COUNT(*) INTO rapid_changes
  FROM account_audit_log
  WHERE user_id = p_user_id
    AND created_at >= NOW() - INTERVAL '1 minute';
  
  IF rapid_changes > 5 THEN
    risk_score := risk_score + 30;
    indicators := indicators || jsonb_build_object('rapid_actions', rapid_changes);
  END IF;
  
  -- Check for unusual number of actions in last hour
  SELECT COUNT(*) INTO recent_actions
  FROM account_audit_log
  WHERE user_id = p_user_id
    AND created_at >= NOW() - INTERVAL '1 hour';
  
  IF recent_actions > 20 THEN
    risk_score := risk_score + 25;
    indicators := indicators || jsonb_build_object('high_activity', recent_actions);
  END IF;
  
  -- Check for multiple IP addresses in short time
  SELECT COUNT(DISTINCT ip_address) INTO unique_ips
  FROM account_audit_log
  WHERE user_id = p_user_id
    AND created_at >= NOW() - INTERVAL '1 hour'
    AND ip_address IS NOT NULL;
  
  IF unique_ips > 3 THEN
    risk_score := risk_score + 40;
    indicators := indicators || jsonb_build_object('multiple_ips', unique_ips);
  END IF;
  
  -- Check for bulk operations outside normal hours (assuming UTC)
  IF p_action LIKE 'bulk_%' AND (EXTRACT(HOUR FROM NOW()) < 6 OR EXTRACT(HOUR FROM NOW()) > 23) THEN
    risk_score := risk_score + 20;
    indicators := indicators || jsonb_build_object('off_hours_bulk', true);
  END IF;
  
  -- Log suspicious activity if risk score is high enough
  IF risk_score >= 50 THEN
    INSERT INTO suspicious_activity (
      user_id,
      activity_type,
      risk_score,
      indicators,
      source_ip,
      user_agent
    ) VALUES (
      p_user_id,
      p_action,
      risk_score,
      indicators,
      p_ip_address,
      p_user_agent
    );
    
    -- Create security alert for high-risk activity
    IF risk_score >= 75 THEN
      INSERT INTO security_alerts (
        user_id,
        alert_type,
        severity,
        title,
        description,
        source_ip,
        user_agent,
        request_details
      ) VALUES (
        p_user_id,
        'suspicious_activity',
        CASE WHEN risk_score >= 90 THEN 'critical' ELSE 'high' END,
        'Suspicious Activity Detected',
        'High-risk activity detected for user account operations',
        p_ip_address,
        p_user_agent,
        jsonb_build_object('risk_score', risk_score, 'indicators', indicators)
      );
    END IF;
  END IF;
END;
$$;

-- Function to get security dashboard data
CREATE OR REPLACE FUNCTION get_security_dashboard(
  p_time_window_hours INTEGER DEFAULT 24
)
RETURNS TABLE (
  total_audit_entries BIGINT,
  unique_users BIGINT,
  security_alerts INTEGER,
  suspicious_activities INTEGER,
  rate_limit_violations INTEGER,
  top_actions JSONB,
  risk_distribution JSONB
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  WITH audit_summary AS (
    SELECT 
      COUNT(*) as total_entries,
      COUNT(DISTINCT user_id) as unique_users
    FROM account_audit_log
    WHERE created_at >= NOW() - (p_time_window_hours || ' hours')::INTERVAL
  ),
  alert_counts AS (
    SELECT 
      COUNT(*) FILTER (WHERE alert_type = 'rate_limit_exceeded') as rate_limits,
      COUNT(*) as total_alerts
    FROM security_alerts
    WHERE created_at >= NOW() - (p_time_window_hours || ' hours')::INTERVAL
      AND NOT resolved
  ),
  action_summary AS (
    SELECT jsonb_object_agg(action, action_count) as actions_json
    FROM (
      SELECT action, COUNT(*) as action_count
      FROM account_audit_log
      WHERE created_at >= NOW() - (p_time_window_hours || ' hours')::INTERVAL
      GROUP BY action
      ORDER BY COUNT(*) DESC
      LIMIT 10
    ) t
  ),
  risk_summary AS (
    SELECT jsonb_object_agg(risk_level, risk_count) as risk_json
    FROM (
      SELECT 
        CASE 
          WHEN risk_score >= 75 THEN 'high'
          WHEN risk_score >= 50 THEN 'medium'
          ELSE 'low'
        END as risk_level,
        COUNT(*) as risk_count
      FROM suspicious_activity
      WHERE created_at >= NOW() - (p_time_window_hours || ' hours')::INTERVAL
      GROUP BY 1
    ) r
  )
  SELECT 
    aus.total_entries,
    aus.unique_users,
    ac.total_alerts::INTEGER,
    (SELECT COUNT(*)::INTEGER FROM suspicious_activity 
     WHERE created_at >= NOW() - (p_time_window_hours || ' hours')::INTERVAL),
    ac.rate_limits::INTEGER,
    COALESCE(acs.actions_json, '{}'::jsonb),
    COALESCE(rs.risk_json, '{}'::jsonb)
  FROM audit_summary aus
  CROSS JOIN alert_counts ac
  CROSS JOIN action_summary acs
  CROSS JOIN risk_summary rs;
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION log_account_audit(UUID, UUID, VARCHAR(50), JSONB, JSONB, TEXT[], TEXT, INET, TEXT, TEXT, TEXT, TEXT, VARCHAR(10), JSONB) TO authenticated;
GRANT EXECUTE ON FUNCTION check_rate_limit(UUID, VARCHAR(50), INTEGER, INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION get_security_dashboard(INTEGER) TO authenticated;

-- Add unique constraint for rate limits
ALTER TABLE rate_limits ADD CONSTRAINT unique_rate_limit_window UNIQUE (user_id, operation_type, window_start);

-- Add comments
COMMENT ON TABLE account_audit_log IS 'Comprehensive audit trail for all account status changes';
COMMENT ON TABLE rate_limits IS 'Rate limiting tracking for pause/resume operations';
COMMENT ON TABLE security_alerts IS 'Security alerts for suspicious or problematic activity';
COMMENT ON TABLE suspicious_activity IS 'Detailed tracking of potentially suspicious user behavior';

COMMENT ON FUNCTION log_account_audit(UUID, UUID, VARCHAR(50), JSONB, JSONB, TEXT[], TEXT, INET, TEXT, TEXT, TEXT, TEXT, VARCHAR(10), JSONB) IS 'Logs comprehensive audit trail for account changes';
COMMENT ON FUNCTION check_rate_limit(UUID, VARCHAR(50), INTEGER, INTEGER) IS 'Checks and enforces rate limits for operations';
COMMENT ON FUNCTION analyze_suspicious_activity(UUID, VARCHAR(50), INET, TEXT) IS 'Analyzes user activity for suspicious patterns';
COMMENT ON FUNCTION get_security_dashboard(INTEGER) IS 'Returns security dashboard data for monitoring';
