[build]
  publish = "dist"

# Default build (used for local netlify-cli unless overridden)
[build.environment]
  NODE_VERSION = "18"

# Production context (main branch)
[context.production]
  command = "npm run build"

# Deploy previews (PRs) and branch deploys (staging)
[context.deploy-preview]
  command = "npm run build:staging"

[context.branch-deploy]
  command = "npm run build:staging"

# Vite SPA redirects for client-side routing
[[redirects]]
  from = "/api/*"
  to = "/.netlify/functions/:splat"
  status = 200

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Optional: headers to tighten security (adjust if something breaks)
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "no-referrer-when-downgrade"
    Permissions-Policy = "geolocation=(), microphone=(), camera=()"
