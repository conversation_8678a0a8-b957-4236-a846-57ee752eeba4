# Vite environment variables for staging (Netlify)
# Copy this file to `.env.staging` locally if you want to run `vite --mode staging`
# In Netlify, set these as Environment Variables in the UI under Site settings > Build & deploy > Environment

# Supabase (GiveRound project)
VITE_SUPABASE_URL=
VITE_SUPABASE_ANON_KEY=

# Plaid (Edge Function uses server-side secret; client only needs environment)
# If your Edge Function reads PLAID_ENV from environment, set it in Netlify too (development|sandbox|production)
PLAID_ENV=development

# Optional app flags
VITE_APP_ENV=staging
VITE_APP_NAME=GiveRound

