import { useState, useEffect } from 'react';
import { LinkedAccounts } from '@/components/LinkedAccounts';
import { PlaidLink } from '@/components/PlaidLink';
import { TransactionSync } from '@/components/TransactionSync';

const Accounts = () => {
  const [refreshKey, setRefreshKey] = useState(0);

  // Force a refresh of child components when this page is mounted
  useEffect(() => {
    setRefreshKey(prev => prev + 1);
  }, []);

  // Handle successful account linking
  const handleAccountLinked = () => {
    // Increment the key to force a refresh of the LinkedAccounts component
    setRefreshKey(prev => prev + 1);
  };

  // Handle successful transaction sync
  const handleTransactionSync = () => {
    // Could trigger a refresh of dashboard or other components if needed
    console.log('Transactions synced successfully');
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <h1 className="text-3xl font-bold">Account Management</h1>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="space-y-6">
          <LinkedAccounts refreshKey={refreshKey} />
          <PlaidLink onSuccess={handleAccountLinked} />
        </div>
        <div>
          <TransactionSync onSyncComplete={handleTransactionSync} />
        </div>
      </div>
    </div>
  );
};

export default Accounts;
