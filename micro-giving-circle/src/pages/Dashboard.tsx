
import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { useAuth } from "@/contexts/AuthContext";
import {
  getUserDashboardSummary,
  getUserTransactions,
  getUserCharityFollows,
  getUserDonations
} from "@/lib/database";
import { createSampleTransactions } from "@/lib/sampleData";
import { TransactionHistory } from "@/components/TransactionHistory";
import { toast } from "sonner";

const Dashboard = () => {
  const { user, userProfile } = useAuth();
  const [dashboardData, setDashboardData] = useState<any>(null);
  const [recentTransactions, setRecentTransactions] = useState<any[]>([]);
  const [favoriteCharities, setFavoriteCharities] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    console.log('Dashboard useEffect triggered. User:', user, 'UserProfile:', userProfile);

    const fetchDashboardData = async () => {
      if (!user) {
        console.log('No user found, skipping dashboard fetch');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        console.log('Fetching dashboard data for user:', user.id);

        // Fetch dashboard summary
        const { data: dashboardSummary, error: dashboardError } = await getUserDashboardSummary(user.id);

        if (dashboardError) {
          console.error('Dashboard summary error:', dashboardError);
          throw dashboardError;
        }

        console.log('Dashboard summary:', dashboardSummary);
        setDashboardData(dashboardSummary);

        // Fetch recent transactions
        const { data: transactions, error: transactionsError } = await getUserTransactions(user.id, 5);

        if (transactionsError) {
          console.error('Transactions error:', transactionsError);
        } else {
          console.log('Recent transactions:', transactions);
          setRecentTransactions(transactions || []);
        }

        // Fetch favorite charities
        const { data: charities, error: charitiesError } = await getUserCharityFollows(user.id);

        if (charitiesError) {
          console.error('Charities error:', charitiesError);
        } else {
          console.log('Favorite charities:', charities);
          setFavoriteCharities(charities || []);
        }

      } catch (error) {
        console.error('Error in dashboard:', error);
        setError('Failed to load dashboard data');

        // Set fallback data
        setDashboardData({
          user_id: user.id,
          full_name: userProfile?.full_name || 'User',
          monthly_goal: 50,
          total_donated_this_month: 0,
          total_round_ups_this_month: 0,
          charities_supported: 0
        });
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [user, userProfile]);

  if (loading) {
    return (
      <div className="min-h-screen bg-background p-4 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-background p-4 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 mb-4">⚠️ Error</div>
          <p className="text-muted-foreground mb-4">{error}</p>
          <Button onClick={() => window.location.reload()}>
            Retry
          </Button>
        </div>
      </div>
    );
  }

  const monthlyGoal = dashboardData?.monthly_goal || 50;
  const currentDonations = dashboardData?.total_donated_this_month || 0;
  const totalRoundUps = dashboardData?.total_round_ups_this_month || 0;
  const totalDonated = dashboardData?.total_donated || 0;
  const progress = monthlyGoal > 0 ? (currentDonations / monthlyGoal) * 100 : 0;

  const handleCreateSampleData = async () => {
    if (!user) return;

    try {
      await createSampleTransactions(user.id);
      toast.success('Sample transactions created! Refresh to see them.');
      // Refresh the data
      window.location.reload();
    } catch (error) {
      toast.error('Failed to create sample transactions');
    }
  };

  return (
    <div className="min-h-screen bg-background p-4">
      <div className="container mx-auto max-w-6xl">
        {/* Header */}
        <div className="mb-8 flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold mb-2">
              Welcome back, {dashboardData?.full_name || 'User'}!
            </h1>
            <p className="text-muted-foreground">
              Here's your giving impact summary
            </p>
          </div>
          <Button
            onClick={() => window.location.reload()}
            variant="outline"
            disabled={loading}
          >
            {loading ? 'Loading...' : 'Refresh'}
          </Button>
        </div>

        {/* Account Status Summary */}
        {dashboardData?.account_summary && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Account Status</CardTitle>
              <CardDescription>
                Overview of your connected accounts and round-up status
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {dashboardData.account_summary.total}
                  </div>
                  <p className="text-sm text-muted-foreground">Total Accounts</p>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {dashboardData.account_summary.active}
                  </div>
                  <p className="text-sm text-muted-foreground">Active Round-ups</p>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">
                    {dashboardData.account_summary.paused}
                  </div>
                  <p className="text-sm text-muted-foreground">Paused</p>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-600">
                    {dashboardData.account_summary.inactive}
                  </div>
                  <p className="text-sm text-muted-foreground">Inactive</p>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">
                    {dashboardData.account_summary.deleted}
                  </div>
                  <p className="text-sm text-muted-foreground">Deleted</p>
                </div>
              </div>

              {/* Round-up Efficiency */}
              {dashboardData.account_summary.total > 0 && (
                <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Round-up Efficiency</span>
                    <span className="text-sm text-muted-foreground">
                      {dashboardData.account_summary.active} of {dashboardData.account_summary.total} accounts active
                    </span>
                  </div>
                  <Progress
                    value={dashboardData.account_summary.total > 0 ? (dashboardData.account_summary.active / dashboardData.account_summary.total) * 100 : 0}
                    className="mt-2"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    {((dashboardData.account_summary.active / dashboardData.account_summary.total) * 100).toFixed(1)}% of your accounts are actively generating round-ups
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader>
              <CardTitle>Round-ups This Month</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                ${totalRoundUps.toFixed(2)}
              </div>
              <p className="text-sm text-muted-foreground">
                From {recentTransactions.length} transactions
              </p>
              {dashboardData?.account_summary && dashboardData.account_summary.active > 0 && (
                <p className="text-xs text-green-600 mt-1">
                  ✓ {dashboardData.account_summary.active} accounts generating round-ups
                </p>
              )}
              {dashboardData?.account_summary && dashboardData.account_summary.paused > 0 && (
                <p className="text-xs text-orange-600 mt-1">
                  ⏸ {dashboardData.account_summary.paused} accounts paused
                </p>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Donated This Month</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                ${currentDonations.toFixed(2)}
              </div>
              <p className="text-sm text-muted-foreground">
                Goal: ${monthlyGoal.toFixed(2)}
              </p>
              <Progress value={progress} className="mt-2" />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Total Transactions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{recentTransactions.length}</div>
              <p className="text-sm text-muted-foreground">Recent activity</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Charities Supported</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {dashboardData?.charities_supported || 0}
              </div>
              <p className="text-sm text-muted-foreground">Organizations</p>
            </CardContent>
          </Card>
        </div>

        {/* Account Insights */}
        {dashboardData?.account_summary && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Account Insights</CardTitle>
              <CardDescription>
                Tips to maximize your giving impact
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {dashboardData.account_summary.paused > 0 && (
                  <div className="flex items-start gap-3 p-3 bg-orange-50 rounded-lg border border-orange-200">
                    <div className="text-orange-600 mt-0.5">⏸</div>
                    <div>
                      <p className="font-medium text-orange-800">
                        You have {dashboardData.account_summary.paused} paused account{dashboardData.account_summary.paused > 1 ? 's' : ''}
                      </p>
                      <p className="text-sm text-orange-700">
                        Resume round-ups to increase your giving impact. Visit the Accounts page to manage your settings.
                      </p>
                    </div>
                  </div>
                )}

                {dashboardData.account_summary.total === 0 && (
                  <div className="flex items-start gap-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
                    <div className="text-blue-600 mt-0.5">💳</div>
                    <div>
                      <p className="font-medium text-blue-800">
                        Connect your first account
                      </p>
                      <p className="text-sm text-blue-700">
                        Link your debit card, credit card, or bank account to start generating round-ups for charity.
                      </p>
                    </div>
                  </div>
                )}

                {dashboardData.account_summary.active > 0 && dashboardData.account_summary.paused === 0 && (
                  <div className="flex items-start gap-3 p-3 bg-green-50 rounded-lg border border-green-200">
                    <div className="text-green-600 mt-0.5">✓</div>
                    <div>
                      <p className="font-medium text-green-800">
                        All accounts are active!
                      </p>
                      <p className="text-sm text-green-700">
                        Great job! All {dashboardData.account_summary.active} of your accounts are generating round-ups for charity.
                      </p>
                    </div>
                  </div>
                )}

                {dashboardData.account_summary.total > 0 && dashboardData.account_summary.active / dashboardData.account_summary.total < 0.5 && (
                  <div className="flex items-start gap-3 p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                    <div className="text-yellow-600 mt-0.5">⚡</div>
                    <div>
                      <p className="font-medium text-yellow-800">
                        Boost your impact
                      </p>
                      <p className="text-sm text-yellow-700">
                        Only {Math.round((dashboardData.account_summary.active / dashboardData.account_summary.total) * 100)}% of your accounts are active.
                        Consider resuming paused accounts to increase your giving potential.
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Transaction History */}
        <TransactionHistory refreshKey={dashboardData?.user_id} />

        {/* Welcome Message */}
        <Card>
          <CardHeader>
            <CardTitle>Welcome to Your Giving Dashboard!</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">
              Your dashboard is ready! Start by connecting your debit card, credit card, or bank account to track round-ups
              and browse charities to support causes you care about.
            </p>
            <div className="mt-4 space-x-4">
              <Button>Connect Card or Account</Button>
              <Button variant="outline">Browse Charities</Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Dashboard;
