
import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { useAuth } from "@/contexts/AuthContext";
import { updateUserProfile } from "@/lib/database";
import { toast } from "sonner";

const Profile = () => {
  const { user, userProfile, refreshUserProfile } = useAuth();
  const [profile, setProfile] = useState({
    firstName: "",
    lastName: "",
    email: "",
    monthlyGoal: 50,
  });
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (user && userProfile) {
      const fullName = userProfile.full_name || user.user_metadata?.full_name || "";
      const [firstName = "", lastName = ""] = fullName.split(" ");

      setProfile({
        firstName,
        lastName,
        email: user.email || "",
        monthlyGoal: userProfile.monthly_goal || 50,
      });
    }
  }, [user, userProfile]);

  const [notifications, setNotifications] = useState({
    emailUpdates: true,
    pushNotifications: true,
    monthlyReports: true,
    donationAlerts: false,
  });

  const [privacy, setPrivacy] = useState({
    profileVisible: true,
    donationHistory: false,
    leaderboards: true,
  });

  const handleProfileUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user || !userProfile) return;

    setLoading(true);
    try {
      const fullName = `${profile.firstName} ${profile.lastName}`.trim();
      await updateUserProfile(user.id, {
        full_name: fullName,
        monthly_goal: profile.monthlyGoal,
      });

      await refreshUserProfile();
      toast.success("Profile updated successfully!");
    } catch (error) {
      console.error("Error updating profile:", error);
      toast.error("Failed to update profile");
    } finally {
      setLoading(false);
    }
  };

  const updateProfile = (field: string, value: string | number) => {
    setProfile(prev => ({ ...prev, [field]: value }));
  };

  const updateNotifications = (field: string, value: boolean) => {
    setNotifications(prev => ({ ...prev, [field]: value }));
  };

  const updatePrivacy = (field: string, value: boolean) => {
    setPrivacy(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className="min-h-screen bg-background p-4">
      <div className="container mx-auto max-w-4xl">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Profile Settings</h1>
          <p className="text-muted-foreground">Manage your account preferences and privacy settings</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Personal Information */}
          <Card>
            <CardHeader>
              <CardTitle>Personal Information</CardTitle>
              <CardDescription>Update your basic profile details</CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleProfileUpdate} className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="firstName">First Name</Label>
                    <Input
                      id="firstName"
                      value={profile.firstName}
                      onChange={(e) => updateProfile("firstName", e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="lastName">Last Name</Label>
                    <Input
                      id="lastName"
                      value={profile.lastName}
                      onChange={(e) => updateProfile("lastName", e.target.value)}
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={profile.email}
                    onChange={(e) => updateProfile("email", e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="monthlyGoal">Monthly Giving Goal ($)</Label>
                  <Input
                    id="monthlyGoal"
                    type="number"
                    min="1"
                    value={profile.monthlyGoal}
                    onChange={(e) => updateProfile("monthlyGoal", parseFloat(e.target.value) || 0)}
                  />
                </div>
                <Button type="submit" className="w-full" disabled={loading}>
                  {loading ? "Updating..." : "Update Profile"}
                </Button>
              </form>
            </CardContent>
          </Card>

          {/* Account Statistics */}
          <Card>
            <CardHeader>
              <CardTitle>Account Summary</CardTitle>
              <CardDescription>Your GiveRound journey so far</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-4 bg-muted/30 rounded-lg">
                  <div className="text-2xl font-bold text-primary">
                    ${userProfile?.total_donated?.toFixed(2) || "0.00"}
                  </div>
                  <div className="text-sm text-muted-foreground">Total Donated</div>
                </div>
                <div className="text-center p-4 bg-muted/30 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">
                    ${userProfile?.monthly_goal?.toFixed(2) || "50.00"}
                  </div>
                  <div className="text-sm text-muted-foreground">Monthly Goal</div>
                </div>
              </div>
              <div className="text-center p-4 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg">
                <div className="text-lg font-semibold text-green-700">
                  Member since {userProfile?.created_at ?
                    new Date(userProfile.created_at).toLocaleDateString('en-US', {
                      month: 'long',
                      year: 'numeric'
                    }) : 'Recently'
                  }
                </div>
                <div className="text-sm text-muted-foreground">
                  {userProfile?.created_at ?
                    Math.floor((Date.now() - new Date(userProfile.created_at).getTime()) / (1000 * 60 * 60 * 24 * 30))
                    : 0} months of giving
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Notification Preferences */}
          <Card>
            <CardHeader>
              <CardTitle>Notification Preferences</CardTitle>
              <CardDescription>Choose how you'd like to be notified</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Email Updates</Label>
                  <div className="text-sm text-muted-foreground">Receive updates about your donations</div>
                </div>
                <Switch
                  checked={notifications.emailUpdates}
                  onCheckedChange={(checked) => updateNotifications("emailUpdates", checked)}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Push Notifications</Label>
                  <div className="text-sm text-muted-foreground">Mobile app notifications</div>
                </div>
                <Switch
                  checked={notifications.pushNotifications}
                  onCheckedChange={(checked) => updateNotifications("pushNotifications", checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Monthly Reports</Label>
                  <div className="text-sm text-muted-foreground">Detailed giving summaries</div>
                </div>
                <Switch
                  checked={notifications.monthlyReports}
                  onCheckedChange={(checked) => updateNotifications("monthlyReports", checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Donation Alerts</Label>
                  <div className="text-sm text-muted-foreground">Real-time transaction notifications</div>
                </div>
                <Switch
                  checked={notifications.donationAlerts}
                  onCheckedChange={(checked) => updateNotifications("donationAlerts", checked)}
                />
              </div>
            </CardContent>
          </Card>

          {/* Privacy Settings */}
          <Card>
            <CardHeader>
              <CardTitle>Privacy Settings</CardTitle>
              <CardDescription>Control your privacy and data sharing</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Public Profile</Label>
                  <div className="text-sm text-muted-foreground">Make your profile visible to others</div>
                </div>
                <Switch
                  checked={privacy.profileVisible}
                  onCheckedChange={(checked) => updatePrivacy("profileVisible", checked)}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Donation History</Label>
                  <div className="text-sm text-muted-foreground">Show donation history publicly</div>
                </div>
                <Switch
                  checked={privacy.donationHistory}
                  onCheckedChange={(checked) => updatePrivacy("donationHistory", checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Leaderboards</Label>
                  <div className="text-sm text-muted-foreground">Participate in giving leaderboards</div>
                </div>
                <Switch
                  checked={privacy.leaderboards}
                  onCheckedChange={(checked) => updatePrivacy("leaderboards", checked)}
                />
              </div>

              <Separator />
              
              <div className="space-y-4">
                <Button variant="destructive" className="w-full">
                  Delete Account
                </Button>
                <div className="text-xs text-muted-foreground text-center">
                  This action cannot be undone. All your data will be permanently deleted.
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Profile;
