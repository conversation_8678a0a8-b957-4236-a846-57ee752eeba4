import { supabase } from '@/integrations/supabase/client';
import type { Database } from '@/integrations/supabase/types';

// Types for our database entities
export interface User {
  id: string;
  email: string;
  full_name?: string;
  avatar_url?: string;
  monthly_goal: number;
  total_donated: number;
  created_at: string;
  updated_at: string;
}

export interface Charity {
  id: number;
  name: string;
  description?: string;
  category_id?: number;
  impact_statement?: string;
  verified: boolean;
  rating: number;
  total_donors: number;
  total_raised: number;
  website_url?: string;
  logo_url?: string;
  ein?: string;
  created_at: string;
  updated_at: string;
  category_name?: string;
  category_icon?: string;
}

export interface Transaction {
  id: number;
  user_id: string;
  vendor_name: string;
  purchase_amount: number;
  round_up_amount: number;
  transaction_date: string;
  card_last_four?: string;
  processed: boolean;
  created_at: string;
  plaid_transaction_id?: string;
  account_id?: string;
}

export interface Donation {
  id: number;
  user_id: string;
  charity_id: number;
  amount: number;
  transaction_ids?: number[];
  status: string;
  donation_date: string;
  created_at: string;
}

export interface UserCharityFollow {
  id: number;
  user_id: string;
  charity_id: number;
  allocation_percentage: number;
  created_at: string;
}

// User functions
export const createUserProfile = async (user: {
  id: string;
  email: string;
  full_name?: string;
}) => {
  const { data, error } = await supabase
    .from('users')
    .insert([user])
    .select()
    .single();
  
  return { data, error };
};

export const getUserProfile = async (userId: string) => {
  const { data, error } = await supabase
    .from('users')
    .select('*')
    .eq('id', userId)
    .single();
  
  return { data, error };
};

export const updateUserProfile = async (userId: string, updates: Partial<User>) => {
  const { data, error } = await supabase
    .from('users')
    .update(updates)
    .eq('id', userId)
    .select()
    .single();
  
  return { data, error };
};

// Charity functions
export const getCharities = async () => {
  // First try the view
  const { data, error } = await supabase
    .from('charities_with_categories')
    .select('*')
    .order('verified', { ascending: false })
    .order('rating', { ascending: false });

  if (data && !error) {
    return { data, error };
  }

  console.warn('charities_with_categories view failed, using basic charities table:', error);

  // Fallback to basic charities table with join
  const { data: charitiesData, error: charitiesError } = await supabase
    .from('charities')
    .select(`
      *,
      charity_categories(
        name,
        icon
      )
    `)
    .order('verified', { ascending: false })
    .order('rating', { ascending: false });

  if (charitiesError) {
    return { data: null, error: charitiesError };
  }

  // Transform the data to match the expected format
  const transformedData = charitiesData?.map(charity => ({
    ...charity,
    category_name: charity.charity_categories?.name,
    category_icon: charity.charity_categories?.icon
  }));

  return { data: transformedData, error: null };
};

export const getCharitiesByCategory = async (category: string) => {
  const { data, error } = await supabase
    .from('charities_with_categories')
    .select('*')
    .eq('category_name', category)
    .order('rating', { ascending: false });
  
  return { data, error };
};

export const getCharityById = async (charityId: number) => {
  const { data, error } = await supabase
    .from('charities_with_categories')
    .select('*')
    .eq('id', charityId)
    .single();
  
  return { data, error };
};

// User charity follows
export const getUserCharityFollows = async (userId: string) => {
  const { data, error } = await supabase
    .from('user_charity_follows')
    .select(`
      *,
      charities:charity_id (
        id,
        name,
        description,
        verified,
        rating,
        total_raised
      )
    `)
    .eq('user_id', userId);
  
  return { data, error };
};

export const followCharity = async (userId: string, charityId: number, allocationPercentage = 0) => {
  const { data, error } = await supabase
    .from('user_charity_follows')
    .insert([{
      user_id: userId,
      charity_id: charityId,
      allocation_percentage: allocationPercentage
    }])
    .select()
    .single();
  
  return { data, error };
};

export const unfollowCharity = async (userId: string, charityId: number) => {
  const { data, error } = await supabase
    .from('user_charity_follows')
    .delete()
    .eq('user_id', userId)
    .eq('charity_id', charityId);
  
  return { data, error };
};

export const updateCharityAllocation = async (
  userId: string, 
  charityId: number, 
  allocationPercentage: number
) => {
  const { data, error } = await supabase
    .from('user_charity_follows')
    .update({ allocation_percentage: allocationPercentage })
    .eq('user_id', userId)
    .eq('charity_id', charityId)
    .select()
    .single();
  
  return { data, error };
};

// Transaction functions
export const createTransaction = async (transaction: Omit<Transaction, 'id' | 'created_at'>) => {
  const { data, error } = await supabase
    .from('transactions')
    .insert([transaction])
    .select()
    .single();
  
  return { data, error };
};

export const getUserTransactions = async (userId: string, limit = 10) => {
  const { data, error } = await supabase
    .from('transactions')
    .select('*')
    .eq('user_id', userId)
    .order('transaction_date', { ascending: false })
    .limit(limit);

  return { data, error };
};

export const getUserTransactionsSummary = async (userId: string) => {
  const { data, error } = await supabase
    .from('transactions')
    .select('purchase_amount, round_up_amount, processed')
    .eq('user_id', userId);

  if (error) return { data: null, error };

  const summary = {
    total_transactions: data?.length || 0,
    total_spent: data?.reduce((sum, t) => sum + (parseFloat(t.purchase_amount) || 0), 0) || 0,
    total_round_ups: data?.reduce((sum, t) => sum + (parseFloat(t.round_up_amount) || 0), 0) || 0,
    processed_round_ups: data?.filter(t => t.processed).reduce((sum, t) => sum + (parseFloat(t.round_up_amount) || 0), 0) || 0,
    pending_round_ups: data?.filter(t => !t.processed).reduce((sum, t) => sum + (parseFloat(t.round_up_amount) || 0), 0) || 0,
  };

  return { data: summary, error: null };
};

// Donation functions
export const createDonation = async (donation: Omit<Donation, 'id' | 'created_at'>) => {
  const { data, error } = await supabase
    .from('donations')
    .insert([donation])
    .select()
    .single();
  
  return { data, error };
};

export const getUserDonations = async (userId: string) => {
  const { data, error } = await supabase
    .from('donations')
    .select(`
      *,
      charities:charity_id (
        id,
        name,
        verified
      )
    `)
    .eq('user_id', userId)
    .order('donation_date', { ascending: false });
  
  return { data, error };
};

// Dashboard summary
export const getUserDashboardSummary = async (userId: string) => {
  console.log('Getting dashboard summary for user:', userId);

  if (!userId) {
    console.error('No user ID provided');
    return { data: null, error: new Error('No user ID provided') };
  }

  // Always use fallback approach for now to avoid RPC issues
  try {
    console.log('Fetching user data...');
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    if (userError) {
      console.error('Failed to fetch user:', userError);
      console.error('Error details:', JSON.stringify(userError, null, 2));
      return { data: null, error: userError };
    }

    console.log('User data fetched successfully:', user);

    const startOfMonth = new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString();
    console.log('Start of month:', startOfMonth);

    const { data: donations, error: donationsError } = await supabase
      .from('donations')
      .select('amount, donation_date')
      .eq('user_id', userId)
      .gte('donation_date', startOfMonth);

    if (donationsError) {
      console.warn('Donations query failed:', donationsError);
    }

    const { data: transactions, error: transactionsError } = await supabase
      .from('transactions')
      .select('round_up_amount, transaction_date')
      .eq('user_id', userId)
      .gte('transaction_date', startOfMonth);

    if (transactionsError) {
      console.warn('Transactions query failed:', transactionsError);
    }

    const { data: charityFollows, error: charityFollowsError } = await supabase
      .from('user_charity_follows')
      .select('charity_id')
      .eq('user_id', userId);

    if (charityFollowsError) {
      console.warn('Charity follows query failed:', charityFollowsError);
    }

    const totalDonatedThisMonth = donations?.reduce((sum, d) => sum + (parseFloat(d.amount) || 0), 0) || 0;
    const totalRoundUpsThisMonth = transactions?.reduce((sum, t) => sum + (parseFloat(t.round_up_amount) || 0), 0) || 0;
    const charitiesSupported = charityFollows?.length || 0;

    // Get account status summary
    const { data: accountSummary } = await getAccountStatusSummary(userId);

    const result = {
      user_id: userId,
      full_name: user.full_name || '',
      monthly_goal: parseFloat(user.monthly_goal) || 50,
      total_donated_this_month: totalDonatedThisMonth,
      total_round_ups_this_month: totalRoundUpsThisMonth,
      charities_supported: charitiesSupported,
      account_summary: accountSummary
    };

    console.log('Dashboard summary result:', result);

    return {
      data: result,
      error: null
    };
  } catch (fallbackError) {
    console.error('Dashboard summary failed completely:', fallbackError);
    return {
      data: null,
      error: fallbackError
    };
  }
};

// Charity categories
export const getCharityCategories = async () => {
  const { data, error } = await supabase
    .from('charity_categories')
    .select('*')
    .order('name');

  return { data, error };
};

// Account status utilities
export type LinkedAccountRow = Database['public']['Tables']['linked_accounts']['Row'];

/**
 * Check if an account should process round-ups
 * @param account - The linked account to check
 * @returns boolean - true if the account should process round-ups
 */
export const shouldProcessRoundUps = (account: LinkedAccountRow): boolean => {
  return !!(
    account.is_active &&           // Account must be active
    account.rounding_enabled &&    // Rounding must be enabled
    !account.deleted_at            // Account must not be soft deleted
  );
};

/**
 * Get all accounts that should process round-ups for a user
 * @param userId - The user ID
 * @returns Promise with accounts that can process round-ups
 */
export const getActiveRoundUpAccounts = async (userId: string) => {
  const { data, error } = await supabase
    .from('linked_accounts')
    .select('*')
    .eq('user_id', userId)
    .eq('is_active', true)
    .eq('rounding_enabled', true)
    .is('deleted_at', null)
    .order('created_at', { ascending: false });

  return { data, error };
};

/**
 * Get account status summary for a user
 * @param userId - The user ID
 * @returns Promise with account status breakdown
 */
export const getAccountStatusSummary = async (userId: string) => {
  const { data: allAccounts, error } = await supabase
    .from('linked_accounts')
    .select('is_active, rounding_enabled, deleted_at')
    .eq('user_id', userId);

  if (error) return { data: null, error };

  const summary = {
    total: allAccounts?.length || 0,
    active: allAccounts?.filter(acc => shouldProcessRoundUps(acc)).length || 0,
    paused: allAccounts?.filter(acc => acc.is_active && !acc.rounding_enabled && !acc.deleted_at).length || 0,
    inactive: allAccounts?.filter(acc => !acc.is_active && !acc.deleted_at).length || 0,
    deleted: allAccounts?.filter(acc => acc.deleted_at).length || 0
  };

  return { data: summary, error: null };
};

// Scheduled pausing utilities

/**
 * Schedule an account to be paused for a specific date range
 * @param accountId - The account ID to schedule
 * @param userId - The user ID (for security)
 * @param startDate - When to start the pause
 * @param endDate - When to end the pause
 * @param reason - Reason for the pause
 * @returns Promise with operation result
 */
export const scheduleAccountPause = async (
  accountId: string,
  userId: string,
  startDate: Date,
  endDate: Date,
  reason: string = 'Scheduled pause'
) => {
  const { data, error } = await supabase.rpc('schedule_account_pause', {
    account_id: accountId,
    user_id: userId,
    pause_start: startDate.toISOString(),
    pause_end: endDate.toISOString(),
    reason: reason
  });

  return { data, error };
};

/**
 * Cancel a scheduled pause for an account
 * @param accountId - The account ID
 * @param userId - The user ID (for security)
 * @returns Promise with operation result
 */
export const cancelScheduledPause = async (accountId: string, userId: string) => {
  const { data, error } = await supabase.rpc('cancel_scheduled_pause', {
    account_id: accountId,
    user_id: userId
  });

  return { data, error };
};

/**
 * Get accounts with scheduled pauses for a user
 * @param userId - The user ID
 * @returns Promise with scheduled accounts
 */
export const getScheduledPauses = async (userId: string) => {
  const { data, error } = await supabase
    .from('linked_accounts')
    .select('id, account_name, mask, scheduled_pause_start, scheduled_pause_end, scheduled_pause_reason, auto_paused, rounding_enabled')
    .eq('user_id', userId)
    .not('scheduled_pause_start', 'is', null)
    .is('deleted_at', null)
    .order('scheduled_pause_start', { ascending: true });

  return { data, error };
};

/**
 * Process scheduled pauses (should be called periodically)
 * @returns Promise with operation result
 */
export const processScheduledPauses = async () => {
  const { data, error } = await supabase.rpc('process_scheduled_pauses');
  return { data, error };
};

// Pause history utilities

/**
 * Get pause history for a user
 * @param userId - The user ID
 * @param limit - Number of records to return (default: 50)
 * @param offset - Number of records to skip (default: 0)
 * @returns Promise with pause history
 */
export const getUserPauseHistory = async (userId: string, limit: number = 50, offset: number = 0) => {
  const { data, error } = await supabase.rpc('get_user_pause_history', {
    p_user_id: userId,
    p_limit: limit,
    p_offset: offset
  });

  return { data, error };
};

/**
 * Get pause history for a specific account
 * @param userId - The user ID (for security)
 * @param accountId - The account ID
 * @param limit - Number of records to return (default: 20)
 * @returns Promise with account pause history
 */
export const getAccountPauseHistory = async (userId: string, accountId: string, limit: number = 20) => {
  const { data, error } = await supabase.rpc('get_account_pause_history', {
    p_user_id: userId,
    p_account_id: accountId,
    p_limit: limit
  });

  return { data, error };
};

/**
 * Manually log a pause action (for cases where automatic logging isn't sufficient)
 * @param userId - The user ID
 * @param accountId - The account ID
 * @param action - The action type
 * @param reason - Reason for the action
 * @param previousState - Previous rounding_enabled state
 * @param newState - New rounding_enabled state
 * @param triggeredBy - Who/what triggered the action
 * @param metadata - Additional metadata
 * @returns Promise with operation result
 */
export const logPauseAction = async (
  userId: string,
  accountId: string,
  action: 'pause' | 'resume' | 'schedule' | 'cancel_schedule',
  reason?: string,
  previousState?: boolean,
  newState?: boolean,
  triggeredBy: 'user' | 'system' | 'scheduled' = 'user',
  metadata?: any
) => {
  const { data, error } = await supabase.rpc('log_pause_action', {
    p_user_id: userId,
    p_account_id: accountId,
    p_action: action,
    p_reason: reason,
    p_previous_state: previousState,
    p_new_state: newState,
    p_triggered_by: triggeredBy,
    p_metadata: metadata ? JSON.stringify(metadata) : null
  });

  return { data, error };
};

// Performance monitoring utilities

/**
 * Log an operation metric for performance monitoring
 * @param userId - The user ID
 * @param operationType - Type of operation (pause, resume, bulk_pause, etc.)
 * @param operationSubtype - Subtype (individual, bulk, scheduled, api)
 * @param accountCount - Number of accounts affected
 * @param executionTimeMs - Execution time in milliseconds
 * @param success - Whether the operation succeeded
 * @param errorCode - Error code if failed
 * @param errorMessage - Error message if failed
 * @param metadata - Additional metadata
 * @returns Promise with operation result
 */
export const logOperationMetric = async (
  userId: string,
  operationType: string,
  operationSubtype?: string,
  accountCount: number = 1,
  executionTimeMs?: number,
  success: boolean = true,
  errorCode?: string,
  errorMessage?: string,
  metadata?: any
) => {
  const { data, error } = await supabase.rpc('log_operation_metric', {
    p_user_id: userId,
    p_operation_type: operationType,
    p_operation_subtype: operationSubtype,
    p_account_count: accountCount,
    p_execution_time_ms: executionTimeMs,
    p_success: success,
    p_error_code: errorCode,
    p_error_message: errorMessage,
    p_user_agent: typeof navigator !== 'undefined' ? navigator.userAgent : 'server',
    p_session_id: typeof sessionStorage !== 'undefined' ? sessionStorage.getItem('session_id') || 'unknown' : 'server',
    p_request_id: crypto.randomUUID(),
    p_metadata: metadata ? JSON.stringify(metadata) : null
  });

  return { data, error };
};

/**
 * Get performance dashboard data
 * @param timeWindowHours - Time window in hours (default: 24)
 * @returns Promise with dashboard data
 */
export const getPerformanceDashboard = async (timeWindowHours: number = 24) => {
  const { data, error } = await supabase.rpc('get_performance_dashboard', {
    p_time_window_hours: timeWindowHours
  });

  return { data: data?.[0] || null, error };
};

/**
 * Get user behavior patterns
 * @param userId - The user ID
 * @returns Promise with user behavior patterns
 */
export const getUserBehaviorPatterns = async (userId: string) => {
  const { data, error } = await supabase
    .from('user_behavior_patterns')
    .select('*')
    .eq('user_id', userId)
    .eq('active', true)
    .order('pattern_score', { ascending: false });

  return { data, error };
};

/**
 * Get performance alerts
 * @param resolved - Filter by resolved status (optional)
 * @param severity - Filter by severity (optional)
 * @returns Promise with performance alerts
 */
export const getPerformanceAlerts = async (resolved?: boolean, severity?: string) => {
  let query = supabase
    .from('performance_alerts')
    .select('*')
    .order('created_at', { ascending: false });

  if (resolved !== undefined) {
    query = query.eq('resolved', resolved);
  }

  if (severity) {
    query = query.eq('severity', severity);
  }

  const { data, error } = await query;
  return { data, error };
};

/**
 * Utility to wrap operations with performance monitoring
 * @param userId - The user ID
 * @param operationType - Type of operation
 * @param operation - The operation function to execute
 * @param operationSubtype - Subtype of operation
 * @param accountCount - Number of accounts affected
 * @returns Promise with operation result and performance data
 */
export const withPerformanceMonitoring = async <T>(
  userId: string,
  operationType: string,
  operation: () => Promise<T>,
  operationSubtype?: string,
  accountCount: number = 1
): Promise<{ result: T; executionTime: number }> => {
  const startTime = Date.now();
  let success = true;
  let errorCode: string | undefined;
  let errorMessage: string | undefined;
  let result: T;

  try {
    result = await operation();
    return { result, executionTime: Date.now() - startTime };
  } catch (error) {
    success = false;
    errorCode = error instanceof Error ? error.name : 'UnknownError';
    errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    throw error;
  } finally {
    const executionTime = Date.now() - startTime;

    // Log the metric (don't await to avoid blocking)
    logOperationMetric(
      userId,
      operationType,
      operationSubtype,
      accountCount,
      executionTime,
      success,
      errorCode,
      errorMessage
    ).catch(console.error);
  }
};

// Security and audit utilities

/**
 * Log an account audit entry for security monitoring
 * @param userId - The user ID
 * @param accountId - The account ID
 * @param action - The action performed
 * @param oldValues - Previous state of the account
 * @param newValues - New state of the account
 * @param changedFields - Array of field names that changed
 * @param reason - User-provided reason
 * @param securityContext - Additional security context
 * @returns Promise with operation result
 */
export const logAccountAudit = async (
  userId: string,
  accountId: string,
  action: string,
  oldValues?: any,
  newValues?: any,
  changedFields?: string[],
  reason?: string,
  securityContext?: any
) => {
  const { data, error } = await supabase.rpc('log_account_audit', {
    p_user_id: userId,
    p_account_id: accountId,
    p_action: action,
    p_old_values: oldValues ? JSON.stringify(oldValues) : null,
    p_new_values: newValues ? JSON.stringify(newValues) : null,
    p_changed_fields: changedFields,
    p_reason: reason,
    p_ip_address: null, // Would be populated server-side in real implementation
    p_user_agent: typeof navigator !== 'undefined' ? navigator.userAgent : 'server',
    p_session_id: typeof sessionStorage !== 'undefined' ? sessionStorage.getItem('session_id') || 'unknown' : 'server',
    p_request_id: crypto.randomUUID(),
    p_api_endpoint: window.location?.pathname || 'unknown',
    p_http_method: 'POST',
    p_security_context: securityContext ? JSON.stringify(securityContext) : null
  });

  return { data, error };
};

/**
 * Check rate limit for an operation
 * @param userId - The user ID
 * @param operationType - Type of operation
 * @param limit - Maximum requests allowed (default: 10)
 * @param windowMinutes - Time window in minutes (default: 60)
 * @returns Promise with boolean indicating if operation is allowed
 */
export const checkRateLimit = async (
  userId: string,
  operationType: string,
  limit: number = 10,
  windowMinutes: number = 60
) => {
  const { data, error } = await supabase.rpc('check_rate_limit', {
    p_user_id: userId,
    p_operation_type: operationType,
    p_limit: limit,
    p_window_minutes: windowMinutes
  });

  return { allowed: data === true, error };
};

/**
 * Get security dashboard data
 * @param timeWindowHours - Time window in hours (default: 24)
 * @returns Promise with security dashboard data
 */
export const getSecurityDashboard = async (timeWindowHours: number = 24) => {
  const { data, error } = await supabase.rpc('get_security_dashboard', {
    p_time_window_hours: timeWindowHours
  });

  return { data: data?.[0] || null, error };
};

/**
 * Get audit logs for a user
 * @param userId - The user ID
 * @param limit - Number of records to return (default: 50)
 * @param offset - Number of records to skip (default: 0)
 * @returns Promise with audit logs
 */
export const getUserAuditLogs = async (userId: string, limit: number = 50, offset: number = 0) => {
  const { data, error } = await supabase
    .from('account_audit_log')
    .select('*')
    .eq('user_id', userId)
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1);

  return { data, error };
};

/**
 * Get security alerts
 * @param resolved - Filter by resolved status (optional)
 * @param severity - Filter by severity (optional)
 * @param limit - Number of records to return (default: 20)
 * @returns Promise with security alerts
 */
export const getSecurityAlerts = async (resolved?: boolean, severity?: string, limit: number = 20) => {
  let query = supabase
    .from('security_alerts')
    .select('*')
    .order('created_at', { ascending: false })
    .limit(limit);

  if (resolved !== undefined) {
    query = query.eq('resolved', resolved);
  }

  if (severity) {
    query = query.eq('severity', severity);
  }

  const { data, error } = await query;
  return { data, error };
};

/**
 * Enhanced operation wrapper with security monitoring
 * @param userId - The user ID
 * @param operationType - Type of operation
 * @param operation - The operation function to execute
 * @param accountId - The account ID being operated on
 * @param reason - User-provided reason
 * @param rateLimit - Rate limit configuration
 * @returns Promise with operation result
 */
export const withSecurityMonitoring = async <T>(
  userId: string,
  operationType: string,
  operation: () => Promise<T>,
  accountId?: string,
  reason?: string,
  rateLimit?: { limit: number; windowMinutes: number }
): Promise<T> => {
  // Check rate limit if configured
  if (rateLimit) {
    const { allowed, error: rateLimitError } = await checkRateLimit(
      userId,
      operationType,
      rateLimit.limit,
      rateLimit.windowMinutes
    );

    if (!allowed) {
      throw new Error('Rate limit exceeded. Please try again later.');
    }
  }

  const startTime = Date.now();
  let success = true;
  let result: T;
  let oldValues: any = null;
  let newValues: any = null;

  try {
    // Get current state if accountId is provided
    if (accountId) {
      const { data: currentAccount } = await supabase
        .from('linked_accounts')
        .select('*')
        .eq('id', accountId)
        .single();
      oldValues = currentAccount;
    }

    // Execute the operation
    result = await operation();

    // Get new state if accountId is provided
    if (accountId) {
      const { data: updatedAccount } = await supabase
        .from('linked_accounts')
        .select('*')
        .eq('id', accountId)
        .single();
      newValues = updatedAccount;
    }

    return result;
  } catch (error) {
    success = false;
    throw error;
  } finally {
    const executionTime = Date.now() - startTime;

    // Log audit entry if accountId is provided
    if (accountId) {
      const changedFields = oldValues && newValues
        ? Object.keys(newValues).filter(key => oldValues[key] !== newValues[key])
        : undefined;

      logAccountAudit(
        userId,
        accountId,
        operationType,
        oldValues,
        newValues,
        changedFields,
        reason,
        {
          execution_time_ms: executionTime,
          success,
          timestamp: new Date().toISOString()
        }
      ).catch(console.error);
    }

    // Log performance metric
    logOperationMetric(
      userId,
      operationType,
      'individual',
      1,
      executionTime,
      success,
      success ? undefined : 'OPERATION_FAILED',
      success ? undefined : 'Operation failed during execution'
    ).catch(console.error);
  }
};

// User engagement utilities

/**
 * Generate smart suggestions for a user
 * @param userId - The user ID
 * @returns Promise with operation result
 */
export const generateSmartSuggestions = async (userId: string) => {
  const { data, error } = await supabase.rpc('generate_smart_suggestions', {
    p_user_id: userId
  });

  return { data, error };
};

/**
 * Get smart suggestions for a user
 * @param userId - The user ID
 * @param status - Filter by status (optional)
 * @returns Promise with suggestions
 */
export const getSmartSuggestions = async (userId: string, status?: string) => {
  let query = supabase
    .from('smart_suggestions')
    .select('*')
    .eq('user_id', userId)
    .order('priority', { ascending: false })
    .order('created_at', { ascending: false });

  if (status) {
    query = query.eq('status', status);
  }

  const { data, error } = await query;
  return { data, error };
};

/**
 * Update suggestion status
 * @param suggestionId - The suggestion ID
 * @param status - New status
 * @returns Promise with operation result
 */
export const updateSuggestionStatus = async (suggestionId: string, status: string) => {
  const { data, error } = await supabase
    .from('smart_suggestions')
    .update({
      status,
      status_updated_at: new Date().toISOString()
    })
    .eq('id', suggestionId);

  return { data, error };
};

/**
 * Create a re-engagement campaign
 * @param userId - The user ID
 * @param campaignType - Type of campaign
 * @param personalizationData - Additional personalization data
 * @returns Promise with campaign ID
 */
export const createReengagementCampaign = async (
  userId: string,
  campaignType: string,
  personalizationData?: any
) => {
  const { data, error } = await supabase.rpc('create_reengagement_campaign', {
    p_user_id: userId,
    p_campaign_type: campaignType,
    p_personalization_data: personalizationData ? JSON.stringify(personalizationData) : null
  });

  return { data, error };
};

/**
 * Calculate impact metrics for a user
 * @param userId - The user ID
 * @param periodStart - Start of the period
 * @param periodEnd - End of the period
 * @returns Promise with impact metrics
 */
export const calculateImpactMetrics = async (
  userId: string,
  periodStart: string,
  periodEnd: string
) => {
  const { data, error } = await supabase.rpc('calculate_impact_metrics', {
    p_user_id: userId,
    p_period_start: periodStart,
    p_period_end: periodEnd
  });

  return { data: data?.[0] || null, error };
};

/**
 * Get re-engagement campaigns for a user
 * @param userId - The user ID
 * @param status - Filter by status (optional)
 * @returns Promise with campaigns
 */
export const getReengagementCampaigns = async (userId: string, status?: string) => {
  let query = supabase
    .from('reengagement_campaigns')
    .select('*')
    .eq('user_id', userId)
    .order('scheduled_for', { ascending: false });

  if (status) {
    query = query.eq('status', status);
  }

  const { data, error } = await query;
  return { data, error };
};

/**
 * Log engagement metric
 * @param userId - The user ID
 * @param metricType - Type of metric
 * @param metricValue - Value of the metric
 * @param contextData - Additional context
 * @returns Promise with operation result
 */
export const logEngagementMetric = async (
  userId: string,
  metricType: string,
  metricValue?: number,
  contextData?: any
) => {
  const { data, error } = await supabase
    .from('user_engagement_metrics')
    .insert({
      user_id: userId,
      metric_type: metricType,
      metric_value: metricValue,
      context_data: contextData
    });

  return { data, error };
};

/**
 * Get engagement metrics for a user
 * @param userId - The user ID
 * @param metricType - Filter by metric type (optional)
 * @param limit - Number of records to return (default: 50)
 * @returns Promise with engagement metrics
 */
export const getEngagementMetrics = async (
  userId: string,
  metricType?: string,
  limit: number = 50
) => {
  let query = supabase
    .from('user_engagement_metrics')
    .select('*')
    .eq('user_id', userId)
    .order('triggered_at', { ascending: false })
    .limit(limit);

  if (metricType) {
    query = query.eq('metric_type', metricType);
  }

  const { data, error } = await query;
  return { data, error };
};


