import { createTransaction } from './database';

// Function to create sample transactions for testing
export const createSampleTransactions = async (userId: string) => {
  const sampleTransactions = [
    {
      user_id: userId,
      vendor_name: "Starbucks",
      purchase_amount: 4.25,
      round_up_amount: 0.75,
      transaction_date: new Date().toISOString(),
      card_last_four: "1234",
      processed: false
    },
    {
      user_id: userId,
      vendor_name: "Target",
      purchase_amount: 23.47,
      round_up_amount: 0.53,
      transaction_date: new Date(Date.now() - 86400000).toISOString(), // Yesterday
      card_last_four: "1234",
      processed: false
    },
    {
      user_id: userId,
      vendor_name: "Gas Station",
      purchase_amount: 45.12,
      round_up_amount: 0.88,
      transaction_date: new Date(Date.now() - 172800000).toISOString(), // 2 days ago
      card_last_four: "1234",
      processed: false
    },
    {
      user_id: userId,
      vendor_name: "Grocery Store",
      purchase_amount: 67.89,
      round_up_amount: 0.11,
      transaction_date: new Date(Date.now() - 259200000).toISOString(), // 3 days ago
      card_last_four: "1234",
      processed: false
    }
  ];

  try {
    for (const transaction of sampleTransactions) {
      await createTransaction(transaction);
    }
    console.log('Sample transactions created successfully');
  } catch (error) {
    console.error('Error creating sample transactions:', error);
  }
};

// Function to calculate round-up amount
export const calculateRoundUp = (amount: number): number => {
  const roundedUp = Math.ceil(amount);
  return roundedUp - amount;
};

// Function to format currency
export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount);
};

// Function to format date
export const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  const now = new Date();
  const diffTime = Math.abs(now.getTime() - date.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays === 1) return 'Today';
  if (diffDays === 2) return 'Yesterday';
  if (diffDays <= 7) return `${diffDays - 1} days ago`;
  
  return date.toLocaleDateString();
};
