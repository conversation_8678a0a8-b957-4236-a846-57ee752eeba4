
import { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CreditCard, Trash2, RefreshCw, Pause, Play, Calendar, Clock } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { useLocation } from 'react-router-dom';
import type { Database } from '@/types/supabase';
import { withPerformanceMonitoring } from '@/lib/database';
import PauseHistory from './PauseHistory';
import { ScheduledPauseDialog } from './ScheduledPauseDialog';

type LinkedAccount = Database['public']['Tables']['linked_accounts']['Row'];

interface LinkedAccountsProps {
  refreshKey?: number;
}

// Create a global variable to track if we've loaded accounts
let accountsLoaded = false;

export const LinkedAccounts = ({ refreshKey }: LinkedAccountsProps) => {
  const [accounts, setAccounts] = useState<LinkedAccount[]>([]);
  const [loading, setLoading] = useState(true);
  const { user, session, loading: authLoading } = useAuth();
  const { toast } = useToast();
  const location = useLocation();

  // Scheduled pause dialog state
  const [scheduleDialogOpen, setScheduleDialogOpen] = useState(false);
  const [selectedAccountId, setSelectedAccountId] = useState<string>('');
  const [selectedAccountName, setSelectedAccountName] = useState<string>('');

  // Debug: Log when accounts state changes
  useEffect(() => {
    console.log('LinkedAccounts: accounts state changed:', accounts.length, 'accounts');
    console.log('LinkedAccounts: accounts data:', accounts);
  }, [accounts]);

  // Debug: Log accounts state changes
  console.log('LinkedAccounts render - accounts state:', accounts, 'length:', accounts.length);
  console.log('LinkedAccounts render - loading:', loading, 'authLoading:', authLoading);
  console.log('LinkedAccounts render - accountsLoaded global:', accountsLoaded);

  const fetchLinkedAccounts = useCallback(async (forceRefresh = false) => {
    if (!user || !session) {
      console.log('LinkedAccounts: No user or session available', { user: !!user, session: !!session });
      return;
    }

    console.log('LinkedAccounts: Fetching accounts for user:', user.id);
    console.log('LinkedAccounts: Force refresh:', forceRefresh);

    try {
      // Clear localStorage cache if force refresh or user changed
      const currentCacheKey = `linkedAccounts_${user.id}`;
      if (forceRefresh) {
        console.log('LinkedAccounts: Clearing cache due to force refresh');
        localStorage.removeItem(currentCacheKey);
        localStorage.removeItem('linkedAccounts'); // Remove old cache key too
      }

      // Ensure Supabase client has the session before making queries
      console.log('LinkedAccounts: Ensuring session is set on Supabase client...');
      const { data: currentSession } = await supabase.auth.getSession();

      if (!currentSession?.session) {
        console.log('LinkedAccounts: Setting session from context...');
        const { error: setSessionError } = await supabase.auth.setSession({
          access_token: session.access_token,
          refresh_token: session.refresh_token
        });

        if (setSessionError) {
          console.error('LinkedAccounts: Error setting session:', setSessionError);
          throw setSessionError;
        }

        // Verify session is now set
        const { data: verifySession } = await supabase.auth.getSession();
        console.log('LinkedAccounts: Session verified after setting:', !!verifySession?.session);
      } else {
        console.log('LinkedAccounts: Session already exists on client');
      }

      // Now query the accounts with RLS properly enforced
      console.log('LinkedAccounts: Querying linked_accounts table with RLS...');

      // Now the main query - only fetch non-deleted accounts
      const { data, error } = await supabase
        .from("linked_accounts")
        .select('*')
        .is('deleted_at', null)  // Only fetch non-deleted accounts
        .order('created_at', { ascending: false });

      console.log('LinkedAccounts: Query result - data:', data, 'error:', error);
      console.log('LinkedAccounts: Non-deleted accounts found:', data?.length || 0);

      console.log('LinkedAccounts: Raw data from query:', data);
      console.log('LinkedAccounts: Query error:', error);
      console.log('LinkedAccounts: Data type:', typeof data, 'Is array:', Array.isArray(data));
      console.log('LinkedAccounts: Data length:', data?.length);

      // If RLS query failed, try explicit user_id query for debugging
      if (!data || data.length === 0) {
        console.log('LinkedAccounts: RLS query returned no data, testing explicit user_id query...');
        const { data: explicitData, error: explicitError } = await supabase
          .from('linked_accounts')
          .select('id, account_name, account_type, account_subtype, mask, is_active, created_at, user_id')
          .eq('user_id', user.id)
          .order('created_at', { ascending: false });

        console.log('LinkedAccounts: Explicit query data:', explicitData);
        console.log('LinkedAccounts: Explicit query error:', explicitError);
        console.log('LinkedAccounts: Explicit query length:', explicitData?.length);
      }

      if (error) {
        console.error('LinkedAccounts: Error fetching accounts:', error);
        throw error;
      }

      // Always use fresh data from database, don't fall back to cache
      console.log('LinkedAccounts: Setting accounts from database:', data?.length || 0);
      console.log('LinkedAccounts: About to call setAccounts with:', data);
      setAccounts(data || []);
      console.log('LinkedAccounts: setAccounts called');
      accountsLoaded = true;

      // Store in localStorage with user-specific key
      if (data && data.length > 0) {
        localStorage.setItem(currentCacheKey, JSON.stringify(data));
        console.log('LinkedAccounts: Cached', data.length, 'accounts for user', user.id);
      }

    } catch (error) {
      console.error('LinkedAccounts: Error in fetchLinkedAccounts:', error);
      // Only use cache as absolute last resort and only for current user
      const currentCacheKey = `linkedAccounts_${user.id}`;
      const cachedAccounts = localStorage.getItem(currentCacheKey);
      if (cachedAccounts && !forceRefresh) {
        console.log('LinkedAccounts: Using cached accounts after error');
        setAccounts(JSON.parse(cachedAccounts));
      } else {
        setAccounts([]);
      }
    } finally {
      console.log('LinkedAccounts: Setting loading to false');
      setLoading(false);
    }
  }, [user]);

  const handlePauseAccount = async (accountId: string) => {
    if (!user) {
      toast({
        title: "Error",
        description: "User not authenticated.",
        variant: "destructive",
      });
      return;
    }

    // Prompt for reason
    const reason = prompt('Why are you pausing round-ups? (optional)\n\nCommon reasons:\n• Vacation/travel\n• Budget constraints\n• Temporary break\n• Account maintenance') || 'Manual pause';

    try {
      const { result } = await withPerformanceMonitoring(
        user.id,
        'pause',
        async () => {
          const { error } = await supabase
            .from('linked_accounts')
            .update({ rounding_enabled: false })
            .eq('id', accountId)
            .eq('user_id', user.id);

          if (error) {
            throw error;
          }

          return { success: true };
        },
        'individual',
        1
      );

      // Update local state
      const updatedAccounts = accounts.map(account =>
        account.id === accountId
          ? { ...account, rounding_enabled: false }
          : account
      );
      setAccounts(updatedAccounts);

      // Update localStorage with user-specific key
      const cacheKey = `linkedAccounts_${user.id}`;
      localStorage.setItem(cacheKey, JSON.stringify(updatedAccounts));

      toast({
        title: "Round-ups paused",
        description: `Round-ups have been paused for this account. Reason: ${reason}`,
      });
    } catch (error) {
      console.error('Error pausing account:', error);
      toast({
        title: "Error pausing round-ups",
        description: "Failed to pause round-ups. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleResumeAccount = async (accountId: string) => {
    if (!user) {
      toast({
        title: "Error",
        description: "User not authenticated.",
        variant: "destructive",
      });
      return;
    }

    // Prompt for reason
    const reason = prompt('Why are you resuming round-ups? (optional)\n\nCommon reasons:\n• Back from vacation\n• Budget improved\n• Ready to donate again\n• Account issue resolved') || 'Manual resume';

    try {
      await withPerformanceMonitoring(
        user.id,
        'resume',
        async () => {
          const { error } = await supabase
            .from('linked_accounts')
            .update({ rounding_enabled: true })
            .eq('id', accountId)
            .eq('user_id', user.id);

          if (error) {
            throw error;
          }

          return { success: true };
        },
        'individual',
        1
      );

      // Update local state
      const updatedAccounts = accounts.map(account =>
        account.id === accountId
          ? { ...account, rounding_enabled: true }
          : account
      );
      setAccounts(updatedAccounts);

      // Update localStorage with user-specific key
      const cacheKey = `linkedAccounts_${user.id}`;
      localStorage.setItem(cacheKey, JSON.stringify(updatedAccounts));

      toast({
        title: "Round-ups resumed",
        description: `Round-ups have been resumed for this account. Reason: ${reason}`,
      });
    } catch (error) {
      console.error('Error resuming account:', error);
      toast({
        title: "Error resuming round-ups",
        description: "Failed to resume round-ups. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleBulkPause = async () => {
    if (!user) {
      toast({
        title: "Error",
        description: "User not authenticated.",
        variant: "destructive",
      });
      return;
    }

    const activeAccounts = accounts.filter(account => account.rounding_enabled !== false);

    if (activeAccounts.length === 0) {
      toast({
        title: "No active accounts",
        description: "All accounts are already paused.",
        variant: "destructive",
      });
      return;
    }

    // Confirmation dialog
    const confirmed = window.confirm(
      `Are you sure you want to pause round-ups for ${activeAccounts.length} account${activeAccounts.length > 1 ? 's' : ''}?\n\n` +
      `This will stop generating round-ups from these accounts until you resume them.`
    );

    if (!confirmed) {
      return;
    }

    try {
      const accountIds = activeAccounts.map(account => account.id);

      await withPerformanceMonitoring(
        user.id,
        'bulk_pause',
        async () => {
          const { error } = await supabase
            .from('linked_accounts')
            .update({ rounding_enabled: false })
            .in('id', accountIds)
            .eq('user_id', user.id);

          if (error) {
            throw error;
          }

          return { success: true };
        },
        'bulk',
        activeAccounts.length
      );

      // Update local state
      const updatedAccounts = accounts.map(account =>
        activeAccounts.some(activeAccount => activeAccount.id === account.id)
          ? { ...account, rounding_enabled: false }
          : account
      );
      setAccounts(updatedAccounts);

      // Update localStorage with user-specific key
      const cacheKey = `linkedAccounts_${user.id}`;
      localStorage.setItem(cacheKey, JSON.stringify(updatedAccounts));

      toast({
        title: "All accounts paused",
        description: `Round-ups have been paused for ${activeAccounts.length} account${activeAccounts.length > 1 ? 's' : ''}.`,
      });
    } catch (error) {
      console.error('Error pausing all accounts:', error);
      toast({
        title: "Error pausing accounts",
        description: "Failed to pause all accounts. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleBulkResume = async () => {
    if (!user) {
      toast({
        title: "Error",
        description: "User not authenticated.",
        variant: "destructive",
      });
      return;
    }

    const pausedAccounts = accounts.filter(account => account.rounding_enabled === false);

    if (pausedAccounts.length === 0) {
      toast({
        title: "No paused accounts",
        description: "All accounts are already active.",
        variant: "destructive",
      });
      return;
    }

    // Confirmation dialog
    const confirmed = window.confirm(
      `Are you sure you want to resume round-ups for ${pausedAccounts.length} account${pausedAccounts.length > 1 ? 's' : ''}?\n\n` +
      `This will start generating round-ups from these accounts again.`
    );

    if (!confirmed) {
      return;
    }

    try {
      const accountIds = pausedAccounts.map(account => account.id);

      await withPerformanceMonitoring(
        user.id,
        'bulk_resume',
        async () => {
          const { error } = await supabase
            .from('linked_accounts')
            .update({ rounding_enabled: true })
            .in('id', accountIds)
            .eq('user_id', user.id);

          if (error) {
            throw error;
          }

          return { success: true };
        },
        'bulk',
        pausedAccounts.length
      );

      // Update local state
      const updatedAccounts = accounts.map(account =>
        pausedAccounts.some(pausedAccount => pausedAccount.id === account.id)
          ? { ...account, rounding_enabled: true }
          : account
      );
      setAccounts(updatedAccounts);

      // Update localStorage with user-specific key
      const cacheKey = `linkedAccounts_${user.id}`;
      localStorage.setItem(cacheKey, JSON.stringify(updatedAccounts));

      toast({
        title: "All accounts resumed",
        description: `Round-ups have been resumed for ${pausedAccounts.length} account${pausedAccounts.length > 1 ? 's' : ''}.`,
      });
    } catch (error) {
      console.error('Error resuming all accounts:', error);
      toast({
        title: "Error resuming accounts",
        description: "Failed to resume all accounts. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleSchedulePause = (accountId: string) => {
    if (!user) {
      toast({
        title: "Error",
        description: "User not authenticated.",
        variant: "destructive",
      });
      return;
    }

    const account = accounts.find(acc => acc.id === accountId);
    setSelectedAccountId(accountId);
    setSelectedAccountName(account?.account_name || `${account?.account_type} Account`);
    setScheduleDialogOpen(true);
  };

  const handleCancelSchedule = (accountId: string) => {
    if (!user) {
      toast({
        title: "Error",
        description: "User not authenticated.",
        variant: "destructive",
      });
      return;
    }

    const account = accounts.find(acc => acc.id === accountId);
    setSelectedAccountId(accountId);
    setSelectedAccountName(account?.account_name || `${account?.account_type} Account`);
    setScheduleDialogOpen(true);
  };

  // Handle schedule updates from dialog
  const handleScheduleUpdated = async () => {
    await fetchLinkedAccounts(true);
  };

  // Helper function to get schedule status
  const getScheduleStatus = (account: any) => {
    if (!account.scheduled_pause_start || !account.scheduled_pause_end) {
      return null;
    }

    const now = new Date();
    const startDate = new Date(account.scheduled_pause_start);
    const endDate = new Date(account.scheduled_pause_end);

    if (now < startDate) {
      return {
        status: 'upcoming',
        label: 'Upcoming',
        color: 'text-blue-600',
        icon: '⏰'
      };
    } else if (now >= startDate && now <= endDate) {
      return {
        status: 'active',
        label: 'Active',
        color: 'text-orange-600',
        icon: '⏸️'
      };
    } else {
      return {
        status: 'expired',
        label: 'Expired',
        color: 'text-gray-500',
        icon: '⏹️'
      };
    }
  };

  const handleRemoveAccount = async (accountId: string) => {
    if (!user) {
      toast({
        title: "Error",
        description: "User not authenticated.",
        variant: "destructive",
      });
      return;
    }

    try {
      const { error } = await supabase
        .from('linked_accounts')
        .update({
          deleted_at: new Date().toISOString(),
          deletion_reason: 'User requested removal'
        })
        .eq('id', accountId)
        .eq('user_id', user.id);

      if (error) {
        throw error;
      }

      // Update local state - remove from display
      const updatedAccounts = accounts.filter(account => account.id !== accountId);
      setAccounts(updatedAccounts);

      // Update localStorage with user-specific key
      const cacheKey = `linkedAccounts_${user.id}`;
      localStorage.setItem(cacheKey, JSON.stringify(updatedAccounts));

      toast({
        title: "Account removed",
        description: "The account has been successfully unlinked.",
      });
    } catch (error) {
      console.error('Error removing account:', error);
      toast({
        title: "Error removing account",
        description: "Failed to remove the account. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Load data on initial mount and when user changes
  useEffect(() => {
    console.log('LinkedAccounts: Main useEffect triggered', {
      authLoading,
      user: !!user,
      userId: user?.id,
      accountsLoaded,
      refreshKey
    });

    // Clear any old cache when user changes
    if (user) {
      const currentCacheKey = `linkedAccounts_${user.id}`;
      // Remove old generic cache
      localStorage.removeItem('linkedAccounts');
    }

    // Fetch fresh data when auth is ready and session is available
    if (!authLoading && user && session) {
      console.log('LinkedAccounts: Auth and session ready, fetching accounts for user:', user.id);
      setLoading(true);
      accountsLoaded = false; // Reset loaded flag for new user
      fetchLinkedAccounts(true); // Force refresh for user change
    } else if (!authLoading && !user) {
      console.log('LinkedAccounts: Auth ready but no user');
      setLoading(false);
      setAccounts([]);
    } else if (!authLoading && user && !session) {
      console.log('LinkedAccounts: User available but no session yet');
      setLoading(true); // Keep loading until session is available
    }
  }, [user, session, authLoading, refreshKey, fetchLinkedAccounts]);

  // Force refresh when location changes
  useEffect(() => {
    console.log('LinkedAccounts: Location changed', location.pathname);
    if (user && location.pathname === '/accounts') {
      console.log('LinkedAccounts: Refreshing accounts after navigation to accounts page');
      // Clear cache before refreshing to ensure fresh data
      const cacheKey = `linkedAccounts_${user.id}`;
      localStorage.removeItem(cacheKey);
      fetchLinkedAccounts(true); // Force refresh when navigating to accounts page
    }
  }, [location.pathname, user, fetchLinkedAccounts]);

  // Debug: Show authentication status
  if (!user && !authLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Linked Accounts
          </CardTitle>
          <CardDescription>
            You need to be logged in to view your accounts
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-red-600 mb-4">Not authenticated. Please log in first.</p>
            <Button
              onClick={async () => {
                try {
                  const { error } = await supabase.auth.signInWithPassword({
                    email: '<EMAIL>',
                    password: 'newpassword123'
                  });
                  if (error) {
                    toast({
                      title: "Login Error",
                      description: error.message,
                      variant: "destructive",
                    });
                  } else {
                    toast({
                      title: "Success",
                      description: "Logged in successfully",
                    });
                    // Force a refresh after login
                    setTimeout(() => window.location.reload(), 1000);
                  }
                } catch (error) {
                  console.error('Login error:', error);
                }
              }}
            >
              <NAME_EMAIL>
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (loading && !accounts.length) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Linked Accounts</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4">Loading accounts...</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Linked Accounts</CardTitle>
            <CardDescription>
              Manage your connected debit cards, credit cards, and bank accounts
            </CardDescription>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              disabled={loading}
              onClick={() => {
                setLoading(true);
                accountsLoaded = false; // Reset the loaded flag
                fetchLinkedAccounts(true); // Force refresh with cache clear
              }}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              {loading ? 'Refreshing...' : 'Refresh'}
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {accounts.length === 0 ? (
          <div className="text-center py-4 text-muted-foreground">
            <p>No accounts linked yet. Link an account to start rounding up your purchases.</p>
            {user && (
              <div className="mt-4 p-3 bg-blue-50 rounded text-sm text-blue-800">
                <p><strong>Troubleshooting:</strong></p>
                <p>• Make sure you're logged in with the correct account</p>
                <p>• Try clicking the "Refresh" button above</p>
                <p>• If you recently linked an account, it may take a moment to appear</p>
                <p className="mt-2 text-xs">Current User: {user.email}</p>
                <p className="mt-1 text-xs">User ID: {user.id}</p>
                <p className="mt-1 text-xs">Loading: {loading.toString()}</p>
                <p className="mt-1 text-xs">Auth Loading: {authLoading.toString()}</p>
                <Button
                  className="mt-2"
                  size="sm"
                  onClick={async () => {
                    console.log('Manual test query starting...');
                    try {
                      const { data, error } = await supabase
                        .from('linked_accounts')
                        .select('id, account_name, account_type, account_subtype, mask, is_active, created_at')
                        .eq('user_id', user.id)
                        .eq('is_active', true);

                      console.log('Manual test query result:', { data, error });
                      toast({
                        title: "Debug Query Result",
                        description: `Found ${data?.length || 0} accounts. Check console for details.`,
                      });
                    } catch (err) {
                      console.error('Manual test query error:', err);
                    }
                  }}
                >
                  Test Query
                </Button>
                <Button
                  className="mt-2 ml-2"
                  size="sm"
                  variant="outline"
                  onClick={async () => {
                    console.log('Checking session and refreshing...');
                    try {
                      // First, let's check the current session
                      const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
                      console.log('Current session check:', { sessionData, sessionError });

                      if (sessionData.session) {
                        console.log('Session exists, refreshing it...');
                        const { data: refreshData, error: refreshError } = await supabase.auth.refreshSession();
                        console.log('Session refresh result:', { refreshData, refreshError });

                        if (refreshError) {
                          console.error('Session refresh failed:', refreshError);
                          toast({
                            title: "Session Refresh Failed",
                            description: "Please try logging out and back in manually.",
                            variant: "destructive",
                          });
                        } else {
                          console.log('Session refreshed successfully');
                          toast({
                            title: "Session Refreshed",
                            description: "Trying to fetch accounts again...",
                          });

                          // Try fetching accounts again
                          setTimeout(() => {
                            fetchLinkedAccounts(true);
                          }, 1000);
                        }
                      } else {
                        console.log('No session found');
                        toast({
                          title: "No Session",
                          description: "Please log in manually.",
                          variant: "destructive",
                        });
                      }
                    } catch (err) {
                      console.error('Session check error:', err);
                    }
                  }}
                >
                  Refresh Session
                </Button>
                <Button
                  className="mt-2 ml-2"
                  size="sm"
                  variant="secondary"
                  onClick={async () => {
                    console.log('=== COMPREHENSIVE DEBUG CHECK ===');
                    try {
                      // Check session
                      const { data: sessionData } = await supabase.auth.getSession();
                      const session = sessionData.session;

                      console.log('1. Session user ID:', session?.user?.id || 'NO SESSION');
                      console.log('2. Context user ID:', user?.id || 'NO USER');
                      console.log('3. Session email:', session?.user?.email || 'NO EMAIL');
                      console.log('4. Context email:', user?.email || 'NO EMAIL');
                      console.log('5. Session exists:', !!session);
                      console.log('6. Access token exists:', !!session?.access_token);

                      // Try to query with RPC function that bypasses RLS
                      console.log('7. Testing RPC function...');
                      const { data: rpcData, error: rpcError } = await supabase.rpc('get_current_user_id');
                      console.log('8. RPC current user ID:', rpcData);
                      console.log('9. RPC error:', rpcError);

                      // Test what auth.uid() returns
                      console.log('10. Testing auth.uid()...');
                      const { data: authUidData, error: authUidError } = await supabase.rpc('get_auth_uid');
                      console.log('11. auth.uid() result:', authUidData);
                      console.log('12. auth.uid() error:', authUidError);

                      // Try the accounts query one more time with detailed logging
                      console.log('13. Testing accounts query...');
                      const { data: accountsData, error: accountsError } = await supabase
                        .from('linked_accounts')
                        .select('id, account_name, user_id')
                        .eq('user_id', user.id)
                        .eq('is_active', true);

                      console.log('14. Accounts query result:', accountsData);
                      console.log('15. Accounts query error:', accountsError);

                      // Try a query without the user_id filter to see if RLS is working
                      console.log('16. Testing query without user_id filter...');
                      const { data: allAccountsData, error: allAccountsError } = await supabase
                        .from('linked_accounts')
                        .select('id, account_name, user_id')
                        .eq('is_active', true);

                      console.log('17. All accounts query result:', allAccountsData);
                      console.log('18. All accounts query error:', allAccountsError);

                      toast({
                        title: "Debug Complete",
                        description: "Check console for detailed debug info",
                      });
                    } catch (err) {
                      console.error('Debug error:', err);
                    }
                  }}
                >
                  Full Debug
                </Button>
              </div>
            )}
          </div>
        ) : (
          <div className="space-y-4">
            {/* Account Status Summary */}
            {(() => {
              const activeCount = accounts.filter(a => a.rounding_enabled !== false).length;
              const pausedCount = accounts.filter(a => a.rounding_enabled === false).length;

              return (
                <div className="flex gap-4 p-3 bg-gray-50 rounded-lg text-sm">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span>{activeCount} Active</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                    <span>{pausedCount} Paused</span>
                  </div>
                  <div className="ml-auto flex items-center gap-2">
                    <span className="text-muted-foreground">Total: {accounts.length} accounts</span>
                    {accounts.length > 1 && (
                      <div className="flex gap-2 ml-4">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={handleBulkPause}
                          disabled={activeCount === 0}
                          title="Pause round-ups for all active accounts"
                        >
                          <Pause className="h-3 w-3 mr-1" />
                          Pause All
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={handleBulkResume}
                          disabled={pausedCount === 0}
                          title="Resume round-ups for all paused accounts"
                        >
                          <Play className="h-3 w-3 mr-1" />
                          Resume All
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              );
            })()}

            {/* Scheduled Pauses Summary */}
            {(() => {
              const scheduledAccounts = accounts.filter(account =>
                (account as any).scheduled_pause_start && (account as any).scheduled_pause_end
              );

              if (scheduledAccounts.length === 0) return null;

              const upcomingCount = scheduledAccounts.filter(account => {
                const status = getScheduleStatus(account);
                return status?.status === 'upcoming';
              }).length;

              const activeCount = scheduledAccounts.filter(account => {
                const status = getScheduleStatus(account);
                return status?.status === 'active';
              }).length;

              return (
                <div className="flex gap-4 p-3 bg-blue-50 rounded-lg text-sm border border-blue-200">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                    <span>{upcomingCount} Upcoming Schedules</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                    <span>{activeCount} Active Schedules</span>
                  </div>
                  <div className="ml-auto flex items-center gap-2">
                    <span className="text-muted-foreground">Total: {scheduledAccounts.length} scheduled</span>
                  </div>
                </div>
              );
            })()}

            {/* Accounts List */}
            <div className="space-y-3">
            {accounts.map((account) => {
              const isRoundingEnabled = account.rounding_enabled !== false; // Default to true if undefined

              return (
                <div
                  key={account.id}
                  className="flex items-center justify-between p-3 border rounded-lg"
                >
                  <div className="flex items-center gap-3">
                    <CreditCard className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <div className="font-medium">
                        {account.account_name || `${account.account_type} Account`}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        ••••{account.mask} • {account.account_subtype}
                      </div>
                      <div className={`text-xs mt-1 ${isRoundingEnabled ? 'text-green-600' : 'text-orange-600'}`}>
                        Round-ups: {isRoundingEnabled ? '✓ Active' : '⏸ Paused'}
                        {(account as any).auto_paused && ' (Auto-paused)'}
                      </div>
                      {(() => {
                        const scheduleStatus = getScheduleStatus(account);
                        if (!scheduleStatus) return null;

                        return (
                          <div className={`text-xs mt-1 ${scheduleStatus.color}`}>
                            {scheduleStatus.icon} {scheduleStatus.label}: {new Date((account as any).scheduled_pause_start).toLocaleDateString()} - {new Date((account as any).scheduled_pause_end).toLocaleDateString()}
                            {(account as any).scheduled_pause_reason && (
                              <div className="text-xs text-muted-foreground mt-0.5">
                                Reason: {(account as any).scheduled_pause_reason}
                              </div>
                            )}
                          </div>
                        );
                      })()}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge
                      variant="secondary"
                      className={isRoundingEnabled
                        ? "bg-green-100 text-green-800"
                        : "bg-orange-100 text-orange-800"
                      }
                    >
                      {isRoundingEnabled ? 'Active' : 'Paused'}
                    </Badge>

                    {isRoundingEnabled ? (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handlePauseAccount(account.id)}
                        title="Pause round-ups for this account"
                      >
                        <Pause className="h-4 w-4" />
                      </Button>
                    ) : (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleResumeAccount(account.id)}
                        title="Resume round-ups for this account"
                      >
                        <Play className="h-4 w-4" />
                      </Button>
                    )}

                    {/* Scheduled Pause Controls */}
                    {(account as any).scheduled_pause_start ? (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleCancelSchedule(account.id)}
                        title="Cancel scheduled pause"
                      >
                        <Calendar className="h-4 w-4 text-blue-600" />
                      </Button>
                    ) : (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleSchedulePause(account.id)}
                        title="Schedule pause"
                      >
                        <Clock className="h-4 w-4" />
                      </Button>
                    )}

                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemoveAccount(account.id)}
                      title="Remove this account"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              );
            })}
            </div>
          </div>
        )}
      </CardContent>

      {/* Pause History Section */}
      {accounts.length > 0 && (
        <div className="mt-6">
          <PauseHistory limit={10} />
        </div>
      )}

      {/* Scheduled Pause Dialog */}
      <ScheduledPauseDialog
        open={scheduleDialogOpen}
        onOpenChange={setScheduleDialogOpen}
        accountId={selectedAccountId}
        accountName={selectedAccountName}
        existingSchedule={(() => {
          const account = accounts.find(acc => acc.id === selectedAccountId);
          if (account && (account as any).scheduled_pause_start && (account as any).scheduled_pause_end) {
            return {
              start: (account as any).scheduled_pause_start,
              end: (account as any).scheduled_pause_end,
              reason: (account as any).scheduled_pause_reason || 'Scheduled pause'
            };
          }
          return null;
        })()}
        onScheduleUpdated={handleScheduleUpdated}
      />
    </Card>
  );
};
