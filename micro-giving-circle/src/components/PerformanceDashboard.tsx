import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Activity, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  TrendingUp, 
  Users,
  Zap,
  BarChart3
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { getPerformanceDashboard, getPerformanceAlerts, getUserBehaviorPatterns } from '@/lib/database';

interface PerformanceData {
  total_operations: number;
  successful_operations: number;
  failed_operations: number;
  error_rate: number;
  avg_execution_time: number;
  operations_by_type: Record<string, number>;
  active_alerts: number;
  top_errors: Array<{
    error_code: string;
    count: number;
    percentage: number;
  }>;
}

interface PerformanceAlert {
  id: string;
  alert_type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  threshold_value: number;
  actual_value: number;
  created_at: string;
}

interface BehaviorPattern {
  id: string;
  pattern_type: string;
  pattern_score: number;
  pattern_data: any;
  first_detected: string;
  last_updated: string;
}

export const PerformanceDashboard = () => {
  const { user } = useAuth();
  const [performanceData, setPerformanceData] = useState<PerformanceData | null>(null);
  const [alerts, setAlerts] = useState<PerformanceAlert[]>([]);
  const [behaviorPatterns, setBehaviorPatterns] = useState<BehaviorPattern[]>([]);
  const [loading, setLoading] = useState(true);
  const [timeWindow, setTimeWindow] = useState(24); // hours

  useEffect(() => {
    const fetchData = async () => {
      if (!user) return;

      try {
        setLoading(true);

        // Fetch performance data
        const { data: perfData, error: perfError } = await getPerformanceDashboard(timeWindow);
        if (perfError) {
          console.error('Error fetching performance data:', perfError);
        } else {
          setPerformanceData(perfData);
        }

        // Fetch alerts
        const { data: alertsData, error: alertsError } = await getPerformanceAlerts(false); // Only unresolved
        if (alertsError) {
          console.error('Error fetching alerts:', alertsError);
        } else {
          setAlerts(alertsData || []);
        }

        // Fetch user behavior patterns
        const { data: patternsData, error: patternsError } = await getUserBehaviorPatterns(user.id);
        if (patternsError) {
          console.error('Error fetching behavior patterns:', patternsError);
        } else {
          setBehaviorPatterns(patternsData || []);
        }
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [user, timeWindow]);

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'high':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatPatternType = (type: string) => {
    return type.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="text-center py-8">
          <Activity className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>Loading performance data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Performance Dashboard</h2>
          <p className="text-muted-foreground">
            Monitor system performance and user behavior patterns
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant={timeWindow === 1 ? 'default' : 'outline'}
            size="sm"
            onClick={() => setTimeWindow(1)}
          >
            1h
          </Button>
          <Button
            variant={timeWindow === 24 ? 'default' : 'outline'}
            size="sm"
            onClick={() => setTimeWindow(24)}
          >
            24h
          </Button>
          <Button
            variant={timeWindow === 168 ? 'default' : 'outline'}
            size="sm"
            onClick={() => setTimeWindow(168)}
          >
            7d
          </Button>
        </div>
      </div>

      {/* Performance Metrics */}
      {performanceData && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Operations</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{performanceData.total_operations}</div>
              <p className="text-xs text-muted-foreground">
                Last {timeWindow} hour{timeWindow > 1 ? 's' : ''}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {(100 - performanceData.error_rate).toFixed(1)}%
              </div>
              <Progress 
                value={100 - performanceData.error_rate} 
                className="mt-2" 
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Response Time</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {performanceData.avg_execution_time ? `${performanceData.avg_execution_time.toFixed(0)}ms` : 'N/A'}
              </div>
              <p className="text-xs text-muted-foreground">
                Average execution time
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Alerts</CardTitle>
              <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${performanceData.active_alerts > 0 ? 'text-red-600' : 'text-green-600'}`}>
                {performanceData.active_alerts}
              </div>
              <p className="text-xs text-muted-foreground">
                Unresolved issues
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Operations by Type */}
      {performanceData && Object.keys(performanceData.operations_by_type).length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Operations by Type
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {Object.entries(performanceData.operations_by_type).map(([type, count]) => (
                <div key={type} className="flex items-center justify-between">
                  <span className="capitalize">{type.replace('_', ' ')}</span>
                  <div className="flex items-center gap-2">
                    <div className="w-32 bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full" 
                        style={{ 
                          width: `${(count / performanceData.total_operations) * 100}%` 
                        }}
                      />
                    </div>
                    <span className="text-sm font-medium w-8 text-right">{count}</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Active Alerts */}
      {alerts.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5" />
              Active Alerts
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {alerts.map((alert) => (
                <div key={alert.id} className={`p-3 rounded-lg border ${getSeverityColor(alert.severity)}`}>
                  <div className="flex items-start justify-between">
                    <div>
                      <h4 className="font-medium">{alert.title}</h4>
                      <p className="text-sm opacity-90">{alert.description}</p>
                      <div className="flex items-center gap-4 mt-2 text-xs">
                        <span>Threshold: {alert.threshold_value}</span>
                        <span>Actual: {alert.actual_value}</span>
                        <span>{new Date(alert.created_at).toLocaleString()}</span>
                      </div>
                    </div>
                    <Badge variant="outline" className={getSeverityColor(alert.severity)}>
                      {alert.severity}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* User Behavior Patterns */}
      {behaviorPatterns.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Your Usage Patterns
            </CardTitle>
            <CardDescription>
              Insights based on your account management behavior
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {behaviorPatterns.map((pattern) => (
                <div key={pattern.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <h4 className="font-medium">{formatPatternType(pattern.pattern_type)}</h4>
                    <p className="text-sm text-muted-foreground">
                      Detected: {new Date(pattern.first_detected).toLocaleDateString()}
                    </p>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold">{pattern.pattern_score.toFixed(0)}%</div>
                    <p className="text-xs text-muted-foreground">Confidence</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Top Errors */}
      {performanceData && performanceData.top_errors.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5" />
              Top Errors
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {performanceData.top_errors.map((error, index) => (
                <div key={index} className="flex items-center justify-between p-2 border rounded">
                  <span className="font-mono text-sm">{error.error_code}</span>
                  <div className="flex items-center gap-2">
                    <span className="text-sm">{error.count} occurrences</span>
                    <Badge variant="outline">{error.percentage}%</Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default PerformanceDashboard;
