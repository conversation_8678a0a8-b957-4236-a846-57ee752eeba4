import { useState } from 'react';
import { Clock } from 'lucide-react';
import { format } from 'date-fns';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Calendar as CalendarComponent } from '@/components/ui/calendar';
import { useToast } from '@/hooks/use-toast';
import { scheduleAccountPause, cancelScheduledPause } from '@/lib/database';
import { useAuth } from '@/contexts/AuthContext';

interface ScheduledPauseDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  accountId: string;
  accountName: string;
  existingSchedule?: {
    start: string;
    end: string;
    reason: string;
  } | null;
  onScheduleUpdated: () => void;
}

const PRESET_REASONS = [
  'Vacation',
  'Budget constraints',
  'Temporary financial planning',
  'Holiday spending',
  'Emergency fund building',
  'Custom'
];

const QUICK_DURATIONS = [
  { label: '1 week', days: 7 },
  { label: '2 weeks', days: 14 },
  { label: '1 month', days: 30 },
  { label: '3 months', days: 90 },
  { label: 'Custom', days: 0 }
];

export const ScheduledPauseDialog = ({
  open,
  onOpenChange,
  accountId,
  accountName,
  existingSchedule,
  onScheduleUpdated
}: ScheduledPauseDialogProps) => {
  const { user } = useAuth();
  const { toast } = useToast();
  
  // Form state
  const [startDate, setStartDate] = useState<Date | undefined>(
    existingSchedule ? new Date(existingSchedule.start) : undefined
  );
  const [endDate, setEndDate] = useState<Date | undefined>(
    existingSchedule ? new Date(existingSchedule.end) : undefined
  );
  const [reason, setReason] = useState(existingSchedule?.reason || '');
  const [customReason, setCustomReason] = useState('');
  const [selectedDuration, setSelectedDuration] = useState<number>(0);
  const [loading, setLoading] = useState(false);

  const isEditing = !!existingSchedule;
  const finalReason = reason === 'Custom' ? customReason : reason;

  // Handle quick duration selection
  const handleDurationSelect = (days: number) => {
    setSelectedDuration(days);
    if (days > 0 && startDate) {
      const newEndDate = new Date(startDate);
      newEndDate.setDate(newEndDate.getDate() + days);
      setEndDate(newEndDate);
    }
  };

  // Handle start date change
  const handleStartDateChange = (date: Date | undefined) => {
    setStartDate(date);
    if (date && selectedDuration > 0) {
      const newEndDate = new Date(date);
      newEndDate.setDate(newEndDate.getDate() + selectedDuration);
      setEndDate(newEndDate);
    }
  };

  // Handle end date change
  const handleEndDateChange = (date: Date | undefined) => {
    setEndDate(date);
  };

  // Validation
  const isValid = () => {
    if (!startDate || !endDate || !finalReason.trim()) return false;
    if (startDate >= endDate) return false;
    if (endDate <= new Date()) return false;
    return true;
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!user || !isValid()) return;

    setLoading(true);
    try {
      const { error } = await scheduleAccountPause(
        accountId,
        user.id,
        startDate!,
        endDate!,
        finalReason
      );

      if (error) {
        toast({
          title: 'Error',
          description: error.message || 'Failed to schedule pause',
          variant: 'destructive'
        });
      } else {
        toast({
          title: 'Success',
          description: isEditing 
            ? 'Scheduled pause updated successfully'
            : 'Pause scheduled successfully'
        });
        onScheduleUpdated();
        onOpenChange(false);
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'An unexpected error occurred',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle cancellation
  const handleCancel = async () => {
    if (!user || !isEditing) return;

    setLoading(true);
    try {
      const { error } = await cancelScheduledPause(accountId, user.id);

      if (error) {
        toast({
          title: 'Error',
          description: error.message || 'Failed to cancel scheduled pause',
          variant: 'destructive'
        });
      } else {
        toast({
          title: 'Success',
          description: 'Scheduled pause cancelled successfully'
        });
        onScheduleUpdated();
        onOpenChange(false);
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'An unexpected error occurred',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  // Reset form when dialog closes
  const handleOpenChange = (open: boolean) => {
    if (!open) {
      setStartDate(existingSchedule ? new Date(existingSchedule.start) : undefined);
      setEndDate(existingSchedule ? new Date(existingSchedule.end) : undefined);
      setReason(existingSchedule?.reason || '');
      setCustomReason('');
      setSelectedDuration(0);
    }
    onOpenChange(open);
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-xl">
            <Clock className="h-6 w-6" />
            {isEditing ? 'Edit Scheduled Pause' : 'Schedule Pause'}
          </DialogTitle>
          <DialogDescription className="text-base">
            {isEditing
              ? `Modify the scheduled pause for ${accountName}`
              : `Schedule when to pause round-ups for ${accountName}. Select dates using the calendars below.`
            }
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Quick Duration Selection */}
          {!isEditing && (
            <div className="space-y-3 p-4 bg-gray-50 rounded-lg border">
              <div className="text-center">
                <Label className="text-base font-semibold">Quick Duration Presets</Label>
                <p className="text-xs text-muted-foreground mt-1">
                  Select a preset duration or choose custom dates below
                </p>
              </div>
              <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
                {QUICK_DURATIONS.map((duration) => (
                  <Button
                    key={duration.label}
                    variant={selectedDuration === duration.days ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => handleDurationSelect(duration.days)}
                    className={selectedDuration === duration.days ? 'ring-2 ring-blue-500' : ''}
                  >
                    {duration.label}
                  </Button>
                ))}
              </div>
              {selectedDuration > 0 && (
                <p className="text-xs text-center text-blue-600 font-medium">
                  ✓ {selectedDuration} days selected - now pick your start date
                </p>
              )}
            </div>
          )}

          {/* Date Selection Section */}
          <div className="space-y-4">
            <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
              {/* Start Date Calendar */}
              <div className="space-y-3">
                <div className="text-center">
                  <Label className="text-base font-semibold">Start Date</Label>
                  <p className="text-xs text-muted-foreground">
                    When should the pause begin?
                  </p>
                  {startDate && (
                    <p className="text-sm font-medium text-blue-600 mt-1">
                      {format(startDate, 'EEEE, MMMM d, yyyy')}
                    </p>
                  )}
                </div>
                <div className="flex justify-center">
                  <CalendarComponent
                    mode="single"
                    selected={startDate}
                    onSelect={handleStartDateChange}
                    disabled={(date) => date < new Date()}
                    className="rounded-md border shadow-sm"
                  />
                </div>
              </div>

              {/* End Date Calendar */}
              <div className="space-y-3">
                <div className="text-center">
                  <Label className="text-base font-semibold">End Date</Label>
                  <p className="text-xs text-muted-foreground">
                    When should the pause end?
                  </p>
                  {endDate && (
                    <p className="text-sm font-medium text-green-600 mt-1">
                      {format(endDate, 'EEEE, MMMM d, yyyy')}
                    </p>
                  )}
                  {!startDate && (
                    <p className="text-xs text-orange-600 mt-1">
                      Please select a start date first
                    </p>
                  )}
                </div>
                <div className="flex justify-center">
                  <CalendarComponent
                    mode="single"
                    selected={endDate}
                    onSelect={handleEndDateChange}
                    disabled={(date) => !startDate || date <= startDate}
                    className="rounded-md border shadow-sm"
                  />
                </div>
              </div>
            </div>

            {/* Selected Date Range Summary */}
            {startDate && endDate && (
              <div className="p-4 bg-gradient-to-r from-blue-50 to-green-50 rounded-lg border border-blue-200">
                <div className="text-center">
                  <p className="text-sm font-medium text-gray-700 mb-2">
                    📅 Selected Date Range
                  </p>
                  <p className="text-xl font-bold text-gray-800 mb-1">
                    {format(startDate, 'MMM d')} - {format(endDate, 'MMM d, yyyy')}
                  </p>
                  <div className="flex justify-center items-center gap-4 text-sm">
                    <span className="text-blue-600 font-medium">
                      ⏰ Duration: {Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))} days
                    </span>
                    {Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) >= 7 && (
                      <span className="text-green-600 font-medium">
                        ({Math.ceil(Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) / 7)} weeks)
                      </span>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Reason */}
          <div className="space-y-2">
            <Label>Reason</Label>
            <Select value={reason} onValueChange={setReason}>
              <SelectTrigger>
                <SelectValue placeholder="Select a reason" />
              </SelectTrigger>
              <SelectContent>
                {PRESET_REASONS.map((presetReason) => (
                  <SelectItem key={presetReason} value={presetReason}>
                    {presetReason}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            {reason === 'Custom' && (
              <Input
                placeholder="Enter custom reason"
                value={customReason}
                onChange={(e) => setCustomReason(e.target.value)}
              />
            )}
          </div>

          {/* Validation Messages */}
          {startDate && endDate && startDate >= endDate && (
            <p className="text-sm text-destructive">
              End date must be after start date
            </p>
          )}
          {endDate && endDate <= new Date() && (
            <p className="text-sm text-destructive">
              End date must be in the future
            </p>
          )}
        </div>

        <DialogFooter className="flex gap-2">
          {isEditing && (
            <Button
              variant="destructive"
              onClick={handleCancel}
              disabled={loading}
            >
              Cancel Schedule
            </Button>
          )}
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={loading}
          >
            Close
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={!isValid() || loading}
          >
            {loading ? 'Saving...' : isEditing ? 'Update Schedule' : 'Schedule Pause'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
