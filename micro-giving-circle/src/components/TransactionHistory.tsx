import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Receipt, DollarSign, Clock, CheckCircle } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { getUserTransactions, getUserTransactionsSummary, Transaction } from '@/lib/database';
import { useToast } from '@/hooks/use-toast';

interface TransactionHistoryProps {
  refreshKey?: number;
}

export const TransactionHistory = ({ refreshKey }: TransactionHistoryProps) => {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [summary, setSummary] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [showAll, setShowAll] = useState(false);
  
  const { user } = useAuth();
  const { toast } = useToast();

  const fetchTransactions = async () => {
    if (!user) return;

    try {
      setLoading(true);
      
      // Fetch recent transactions
      const { data: transactionData, error: transactionError } = await getUserTransactions(
        user.id, 
        showAll ? 100 : 10
      );
      
      if (transactionError) {
        throw transactionError;
      }

      // Fetch summary
      const { data: summaryData, error: summaryError } = await getUserTransactionsSummary(user.id);
      
      if (summaryError) {
        throw summaryError;
      }

      setTransactions(transactionData || []);
      setSummary(summaryData);
      
    } catch (error) {
      console.error('Error fetching transactions:', error);
      toast({
        title: "Failed to load transactions",
        description: "Please try again later.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTransactions();
  }, [user, refreshKey, showAll]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Receipt className="h-5 w-5" />
            Transaction History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-2 text-muted-foreground">Loading transactions...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Receipt className="h-5 w-5" />
          Transaction History
        </CardTitle>
        <CardDescription>
          Your recent purchases and round-up donations
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Summary Stats */}
        {summary && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <div className="text-center p-3 bg-blue-50 rounded-lg">
              <div className="text-lg font-semibold text-blue-900">
                {summary.total_transactions}
              </div>
              <div className="text-xs text-blue-600">Total Transactions</div>
            </div>
            
            <div className="text-center p-3 bg-green-50 rounded-lg">
              <div className="text-lg font-semibold text-green-900">
                {formatCurrency(summary.total_round_ups)}
              </div>
              <div className="text-xs text-green-600">Total Round-ups</div>
            </div>
            
            <div className="text-center p-3 bg-orange-50 rounded-lg">
              <div className="text-lg font-semibold text-orange-900">
                {formatCurrency(summary.pending_round_ups)}
              </div>
              <div className="text-xs text-orange-600">Pending</div>
            </div>
            
            <div className="text-center p-3 bg-purple-50 rounded-lg">
              <div className="text-lg font-semibold text-purple-900">
                {formatCurrency(summary.processed_round_ups)}
              </div>
              <div className="text-xs text-purple-600">Donated</div>
            </div>
          </div>
        )}

        {/* Transaction List */}
        {transactions.length === 0 ? (
          <div className="text-center py-8">
            <Receipt className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground mb-2">No transactions found</p>
            <p className="text-sm text-muted-foreground">
              Link an account and sync transactions to see your purchase history.
            </p>
          </div>
        ) : (
          <div className="space-y-3">
            {transactions.map((transaction) => (
              <div
                key={transaction.id}
                className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50"
              >
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-primary/10 rounded-full">
                    <DollarSign className="h-4 w-4 text-primary" />
                  </div>
                  <div>
                    <div className="font-medium">{transaction.vendor_name}</div>
                    <div className="text-sm text-muted-foreground">
                      {formatDate(transaction.transaction_date)}
                      {transaction.card_last_four && (
                        <span className="ml-2">••••{transaction.card_last_four}</span>
                      )}
                    </div>
                  </div>
                </div>
                
                <div className="text-right">
                  <div className="font-medium">
                    {formatCurrency(transaction.purchase_amount)}
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-green-600">
                      +{formatCurrency(transaction.round_up_amount)} round-up
                    </span>
                    {transaction.processed ? (
                      <Badge variant="secondary" className="text-xs">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Donated
                      </Badge>
                    ) : (
                      <Badge variant="outline" className="text-xs">
                        <Clock className="h-3 w-3 mr-1" />
                        Pending
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
            ))}
            
            {!showAll && transactions.length >= 10 && (
              <div className="text-center pt-4">
                <Button
                  variant="outline"
                  onClick={() => setShowAll(true)}
                >
                  Show More Transactions
                </Button>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
