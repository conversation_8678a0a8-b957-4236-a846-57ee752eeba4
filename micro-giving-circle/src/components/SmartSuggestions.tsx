import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Lightbulb, 
  X, 
  Check, 
  Calendar, 
  TrendingUp, 
  Pause, 
  Play,
  Target,
  Clock
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { 
  getSmartSuggestions, 
  updateSuggestionStatus, 
  generateSmartSuggestions,
  logEngagementMetric 
} from '@/lib/database';

interface SmartSuggestion {
  id: string;
  suggestion_type: string;
  title: string;
  description: string;
  confidence_score: number;
  reasoning: any;
  suggested_action: any;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  expires_at: string;
  status: 'active' | 'accepted' | 'dismissed' | 'expired';
  created_at: string;
}

export const SmartSuggestions = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [suggestions, setSuggestions] = useState<SmartSuggestion[]>([]);
  const [loading, setLoading] = useState(true);
  const [generatingNew, setGeneratingNew] = useState(false);

  useEffect(() => {
    const fetchSuggestions = async () => {
      if (!user) return;

      try {
        setLoading(true);
        const { data, error } = await getSmartSuggestions(user.id, 'active');
        
        if (error) {
          console.error('Error fetching suggestions:', error);
        } else {
          setSuggestions(data || []);
        }
      } catch (error) {
        console.error('Error fetching suggestions:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchSuggestions();
  }, [user]);

  const handleAcceptSuggestion = async (suggestion: SmartSuggestion) => {
    if (!user) return;

    try {
      const { error } = await updateSuggestionStatus(suggestion.id, 'accepted');
      
      if (error) {
        throw error;
      }

      // Log engagement metric
      await logEngagementMetric(
        user.id,
        'suggestion_accepted',
        suggestion.confidence_score,
        {
          suggestion_type: suggestion.suggestion_type,
          suggestion_id: suggestion.id
        }
      );

      // Update local state
      setSuggestions(prev => prev.filter(s => s.id !== suggestion.id));

      toast({
        title: "Suggestion accepted",
        description: "We'll help you implement this suggestion.",
      });

      // Handle specific suggestion actions
      handleSuggestionAction(suggestion);
    } catch (error) {
      console.error('Error accepting suggestion:', error);
      toast({
        title: "Error",
        description: "Failed to accept suggestion. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleDismissSuggestion = async (suggestion: SmartSuggestion) => {
    if (!user) return;

    try {
      const { error } = await updateSuggestionStatus(suggestion.id, 'dismissed');
      
      if (error) {
        throw error;
      }

      // Log engagement metric
      await logEngagementMetric(
        user.id,
        'suggestion_dismissed',
        suggestion.confidence_score,
        {
          suggestion_type: suggestion.suggestion_type,
          suggestion_id: suggestion.id
        }
      );

      // Update local state
      setSuggestions(prev => prev.filter(s => s.id !== suggestion.id));

      toast({
        title: "Suggestion dismissed",
        description: "We won't show this suggestion again.",
      });
    } catch (error) {
      console.error('Error dismissing suggestion:', error);
      toast({
        title: "Error",
        description: "Failed to dismiss suggestion. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleSuggestionAction = (suggestion: SmartSuggestion) => {
    // Handle specific actions based on suggestion type
    switch (suggestion.suggestion_type) {
      case 'pause_for_vacation':
        // Navigate to scheduled pause setup
        toast({
          title: "Let's set up your vacation pause",
          description: "Navigate to your accounts to schedule pause dates.",
        });
        break;
      
      case 'resume_reminder':
        // Navigate to paused accounts
        toast({
          title: "Review your paused accounts",
          description: "Check which accounts you'd like to resume.",
        });
        break;
      
      case 'optimize_donations':
        // Navigate to account overview
        toast({
          title: "Optimize your donations",
          description: "Review your account settings for maximum impact.",
        });
        break;
      
      case 'pause_for_budget':
        // Navigate to budget pause setup
        toast({
          title: "Set up a budget break",
          description: "We'll help you schedule a temporary pause.",
        });
        break;
      
      default:
        break;
    }
  };

  const generateNewSuggestions = async () => {
    if (!user) return;

    try {
      setGeneratingNew(true);
      const { error } = await generateSmartSuggestions(user.id);
      
      if (error) {
        throw error;
      }

      // Refresh suggestions
      const { data, error: fetchError } = await getSmartSuggestions(user.id, 'active');
      if (fetchError) {
        throw fetchError;
      }

      setSuggestions(data || []);

      toast({
        title: "Suggestions updated",
        description: "We've generated new personalized suggestions for you.",
      });
    } catch (error) {
      console.error('Error generating suggestions:', error);
      toast({
        title: "Error",
        description: "Failed to generate new suggestions. Please try again.",
        variant: "destructive",
      });
    } finally {
      setGeneratingNew(false);
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'high':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'low':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getSuggestionIcon = (type: string) => {
    switch (type) {
      case 'pause_for_vacation':
        return <Calendar className="h-5 w-5" />;
      case 'resume_reminder':
        return <Play className="h-5 w-5" />;
      case 'optimize_donations':
        return <Target className="h-5 w-5" />;
      case 'pause_for_budget':
        return <Pause className="h-5 w-5" />;
      default:
        return <Lightbulb className="h-5 w-5" />;
    }
  };

  const formatSuggestionType = (type: string) => {
    return type.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="text-center py-8">
          <Lightbulb className="h-8 w-8 animate-pulse mx-auto mb-4" />
          <p>Loading smart suggestions...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <Lightbulb className="h-5 w-5" />
            Smart Suggestions
          </h3>
          <p className="text-sm text-muted-foreground">
            Personalized recommendations to optimize your donations
          </p>
        </div>
        <Button
          onClick={generateNewSuggestions}
          disabled={generatingNew}
          size="sm"
          variant="outline"
        >
          {generatingNew ? (
            <>
              <Clock className="h-4 w-4 mr-2 animate-spin" />
              Generating...
            </>
          ) : (
            <>
              <TrendingUp className="h-4 w-4 mr-2" />
              Refresh Suggestions
            </>
          )}
        </Button>
      </div>

      {/* Suggestions */}
      {suggestions.length === 0 ? (
        <Card>
          <CardContent className="text-center py-8">
            <Lightbulb className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-lg font-medium mb-2">No suggestions available</h3>
            <p className="text-muted-foreground mb-4">
              You're all set! We'll generate new suggestions as your donation patterns evolve.
            </p>
            <Button onClick={generateNewSuggestions} disabled={generatingNew}>
              Generate Suggestions
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {suggestions.map((suggestion) => (
            <Card key={suggestion.id} className="relative">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-3">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      {getSuggestionIcon(suggestion.suggestion_type)}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <CardTitle className="text-base">{suggestion.title}</CardTitle>
                        <Badge 
                          variant="outline" 
                          className={getPriorityColor(suggestion.priority)}
                        >
                          {suggestion.priority}
                        </Badge>
                      </div>
                      <CardDescription className="text-sm">
                        {suggestion.description}
                      </CardDescription>
                    </div>
                  </div>
                  <div className="flex items-center gap-1">
                    <Button
                      size="sm"
                      onClick={() => handleAcceptSuggestion(suggestion)}
                      className="h-8 w-8 p-0"
                    >
                      <Check className="h-4 w-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleDismissSuggestion(suggestion)}
                      className="h-8 w-8 p-0"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between text-xs text-muted-foreground">
                  <span>
                    Type: {formatSuggestionType(suggestion.suggestion_type)}
                  </span>
                  <span>
                    Confidence: {suggestion.confidence_score}%
                  </span>
                  <span>
                    Expires: {new Date(suggestion.expires_at).toLocaleDateString()}
                  </span>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default SmartSuggestions;
