import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { History, Pause, Play, Calendar, Clock, User, Bot } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { getUserPauseHistory, getAccountPauseHistory } from '@/lib/database';

interface PauseHistoryProps {
  accountId?: string; // If provided, shows history for specific account
  limit?: number;
}

interface PauseHistoryEntry {
  id: string;
  account_id: string;
  account_name: string;
  action: 'pause' | 'resume' | 'schedule' | 'cancel_schedule';
  reason: string;
  previous_state: boolean;
  new_state: boolean;
  scheduled_start: string | null;
  scheduled_end: string | null;
  triggered_by: 'user' | 'system' | 'scheduled';
  created_at: string;
  metadata: any;
}

export const PauseHistory = ({ accountId, limit = 20 }: PauseHistoryProps) => {
  const { user } = useAuth();
  const [history, setHistory] = useState<PauseHistoryEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchHistory = async () => {
      if (!user) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        let data, error;
        if (accountId) {
          ({ data, error } = await getAccountPauseHistory(user.id, accountId, limit));
        } else {
          ({ data, error } = await getUserPauseHistory(user.id, limit));
        }

        if (error) {
          throw error;
        }

        setHistory(data || []);
      } catch (err) {
        console.error('Error fetching pause history:', err);
        setError(err instanceof Error ? err.message : 'Failed to load history');
      } finally {
        setLoading(false);
      }
    };

    fetchHistory();
  }, [user, accountId, limit]);

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'pause':
        return <Pause className="h-4 w-4" />;
      case 'resume':
        return <Play className="h-4 w-4" />;
      case 'schedule':
        return <Calendar className="h-4 w-4" />;
      case 'cancel_schedule':
        return <Clock className="h-4 w-4" />;
      default:
        return <History className="h-4 w-4" />;
    }
  };

  const getActionColor = (action: string) => {
    switch (action) {
      case 'pause':
        return 'text-orange-600 bg-orange-100';
      case 'resume':
        return 'text-green-600 bg-green-100';
      case 'schedule':
        return 'text-blue-600 bg-blue-100';
      case 'cancel_schedule':
        return 'text-gray-600 bg-gray-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getTriggerIcon = (triggeredBy: string) => {
    switch (triggeredBy) {
      case 'user':
        return <User className="h-3 w-3" />;
      case 'system':
      case 'scheduled':
        return <Bot className="h-3 w-3" />;
      default:
        return <User className="h-3 w-3" />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const formatAction = (action: string) => {
    switch (action) {
      case 'pause':
        return 'Paused';
      case 'resume':
        return 'Resumed';
      case 'schedule':
        return 'Scheduled';
      case 'cancel_schedule':
        return 'Cancelled Schedule';
      default:
        return action;
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <History className="h-5 w-5" />
            Pause History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4">Loading history...</div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <History className="h-5 w-5" />
            Pause History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4 text-red-600">
            Error loading history: {error}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <History className="h-5 w-5" />
          Pause History
        </CardTitle>
        <CardDescription>
          {accountId 
            ? 'History of pause/resume actions for this account'
            : 'Recent pause/resume actions across all accounts'
          }
        </CardDescription>
      </CardHeader>
      <CardContent>
        {history.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <History className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No pause history found</p>
            <p className="text-sm">Actions will appear here when you pause or resume accounts</p>
          </div>
        ) : (
          <div className="space-y-3">
            {history.map((entry) => (
              <div
                key={entry.id}
                className="flex items-start gap-3 p-3 border rounded-lg hover:bg-gray-50"
              >
                <div className={`p-2 rounded-full ${getActionColor(entry.action)}`}>
                  {getActionIcon(entry.action)}
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="font-medium">
                      {formatAction(entry.action)}
                    </span>
                    {!accountId && (
                      <Badge variant="outline" className="text-xs">
                        {entry.account_name}
                      </Badge>
                    )}
                    <div className="flex items-center gap-1 text-xs text-muted-foreground">
                      {getTriggerIcon(entry.triggered_by)}
                      <span className="capitalize">{entry.triggered_by}</span>
                    </div>
                  </div>
                  
                  {entry.reason && (
                    <p className="text-sm text-muted-foreground mb-1">
                      {entry.reason}
                    </p>
                  )}
                  
                  {entry.scheduled_start && entry.scheduled_end && (
                    <p className="text-xs text-blue-600">
                      Scheduled: {new Date(entry.scheduled_start).toLocaleDateString()} - {new Date(entry.scheduled_end).toLocaleDateString()}
                    </p>
                  )}
                  
                  <p className="text-xs text-muted-foreground">
                    {formatDate(entry.created_at)}
                  </p>
                </div>
                
                <div className="flex items-center gap-1">
                  {entry.previous_state !== null && entry.new_state !== null && (
                    <div className="text-xs text-muted-foreground">
                      {entry.previous_state ? '✓' : '⏸'} → {entry.new_state ? '✓' : '⏸'}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
        
        {history.length >= limit && (
          <div className="text-center mt-4">
            <Button variant="outline" size="sm">
              Load More
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default PauseHistory;
