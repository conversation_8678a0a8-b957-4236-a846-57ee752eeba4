import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { RefreshCw, DollarSign, TrendingUp } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { getUserTransactionsSummary } from '@/lib/database';

interface TransactionSyncProps {
  onSyncComplete?: () => void;
}

export const TransactionSync = ({ onSyncComplete }: TransactionSyncProps) => {
  const [loading, setLoading] = useState(false);
  const [lastSync, setLastSync] = useState<Date | null>(null);
  const [syncStats, setSyncStats] = useState<{
    transactionsProcessed: number;
    totalRoundUps: number;
  } | null>(null);

  const { toast } = useToast();
  const { session, user } = useAuth();

  // Helper function to fetch and update transaction summary
  const fetchTransactionSummary = async () => {
    if (!user) return;

    try {
      const { data, error } = await getUserTransactionsSummary(user.id);

      if (error) {
        console.error('Error fetching transaction summary:', error);
        return;
      }

      if (data) {
        setSyncStats({
          transactionsProcessed: data.total_transactions || 0,
          totalRoundUps: data.total_round_ups || 0
        });
      }
    } catch (error) {
      console.error('Error in fetchTransactionSummary:', error);
    }
  };

  // Fetch current transaction summary on component load
  useEffect(() => {
    fetchTransactionSummary();
  }, [user]);

  const handleSyncTransactions = async () => {
    if (!session || !user) {
      toast({
        title: "Authentication required",
        description: "Please log in to sync transactions.",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    
    try {
      console.log('Starting transaction sync...');
      
      const { data, error } = await supabase.functions.invoke('plaid-operations', {
        body: {
          action: 'fetch_transactions',
        },
        headers: {
          Authorization: `Bearer ${session.access_token}`,
        },
      });

      console.log('Transaction sync response:', { data, error });

      if (error) {
        console.error('Supabase function error:', error);
        throw error;
      }

      if (data?.error) {
        console.error('Plaid API error:', data.error);
        throw new Error(data.error);
      }

      if (!data) {
        throw new Error('No response data received');
      }

      // Calculate round-ups from this sync
      const syncRoundUps = data.transactions?.reduce((sum: number, t: any) =>
        sum + (parseFloat(t.round_up_amount) || 0), 0) || 0;

      setLastSync(new Date());

      toast({
        title: "Transactions synced successfully!",
        description: `Processed ${data.transactions_processed || 0} transactions with $${syncRoundUps.toFixed(2)} in round-ups.`,
      });

      // Refresh the total transaction summary
      await fetchTransactionSummary();

      // Notify parent component
      onSyncComplete?.();

    } catch (error) {
      console.error('Transaction sync error:', error);
      toast({
        title: "Failed to sync transactions",
        description: error instanceof Error ? error.message : "Please try again later.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleAddSampleTransactions = async () => {
    console.log('=== Starting sample transaction creation ===');

    try {
      // First, let's check the current session more thoroughly
      const { data: { session: currentSession }, error: sessionError } = await supabase.auth.getSession();

      console.log('Current session check:', {
        session: currentSession ? {
          user_id: currentSession.user?.id,
          email: currentSession.user?.email,
          expires_at: currentSession.expires_at
        } : null,
        sessionError
      });

      if (sessionError) {
        throw new Error(`Session error: ${sessionError.message}`);
      }

      if (!currentSession?.user) {
        throw new Error('No authenticated user found. Please log in first.');
      }

      console.log('✅ User authenticated:', currentSession.user.id);

      setLoading(true);

      // Create a simple transaction
      const sampleTransaction = {
        user_id: currentSession.user.id,
        vendor_name: 'Test Store',
        purchase_amount: 5.25,
        round_up_amount: 0.75,
        transaction_date: new Date().toISOString(),
        processed: false
      };

      console.log('Inserting transaction:', sampleTransaction);

      const { data, error } = await supabase
        .from('transactions')
        .insert([sampleTransaction])
        .select();

      if (error) {
        console.error('❌ Insert error:', {
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code
        });
        throw error;
      }

      console.log('✅ Transaction created:', data);

      setLastSync(new Date());

      toast({
        title: "Sample Transaction Added",
        description: `Added 1 sample transaction with $0.75 in round-ups.`,
      });

      // Refresh the total transaction summary
      await fetchTransactionSummary();

      onSyncComplete?.();

    } catch (error: any) {
      console.error('❌ Transaction creation failed:', error);
      toast({
        title: "Failed to Add Sample Transactions",
        description: error.message || "Failed to add sample transactions. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <RefreshCw className="h-5 w-5" />
          Transaction Sync
        </CardTitle>
        <CardDescription>
          Fetch your latest transactions and calculate round-ups for donations.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex flex-col gap-4">
          <div className="space-y-2">
            <Button
              onClick={handleSyncTransactions}
              disabled={loading || !session}
              className="w-full"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              {loading ? 'Syncing Transactions...' : 'Sync Transactions'}
            </Button>

            <Button
              onClick={handleAddSampleTransactions}
              disabled={loading || !session}
              variant="outline"
              className="w-full"
            >
              <DollarSign className="h-4 w-4 mr-2" />
              Add Sample Transactions (Test)
            </Button>
          </div>

          {lastSync && (
            <div className="text-sm text-muted-foreground">
              Last synced: {lastSync.toLocaleString()}
            </div>
          )}

          {syncStats && (
            <div className="grid grid-cols-2 gap-4 mt-4">
              <div className="flex items-center gap-2 p-3 bg-blue-50 rounded-lg">
                <DollarSign className="h-4 w-4 text-blue-600" />
                <div>
                  <div className="text-sm font-medium text-blue-900">
                    {syncStats.transactionsProcessed}
                  </div>
                  <div className="text-xs text-blue-600">
                    Transactions
                  </div>
                </div>
              </div>
              
              <div className="flex items-center gap-2 p-3 bg-green-50 rounded-lg">
                <TrendingUp className="h-4 w-4 text-green-600" />
                <div>
                  <div className="text-sm font-medium text-green-900">
                    ${syncStats.totalRoundUps.toFixed(2)}
                  </div>
                  <div className="text-xs text-green-600">
                    Round-ups
                  </div>
                </div>
              </div>
            </div>
          )}

          <div className="text-xs text-muted-foreground">
            <p>• Fetches transactions from the last 30 days</p>
            <p>• Calculates round-ups automatically</p>
            <p>• Only processes purchase transactions</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
