import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Shield, 
  AlertTriangle, 
  Eye, 
  Clock, 
  Users,
  Activity,
  Lock,
  FileText,
  TrendingUp
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { getSecurityDashboard, getSecurityAlerts, getUserAuditLogs } from '@/lib/database';

interface SecurityData {
  total_audit_entries: number;
  unique_users: number;
  security_alerts: number;
  suspicious_activities: number;
  rate_limit_violations: number;
  top_actions: Record<string, number>;
  risk_distribution: Record<string, number>;
}

interface SecurityAlert {
  id: string;
  alert_type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  source_ip: string;
  user_agent: string;
  created_at: string;
  resolved: boolean;
}

interface AuditLog {
  id: string;
  action: string;
  reason: string;
  ip_address: string;
  user_agent: string;
  created_at: string;
  changed_fields: string[];
}

export const SecurityDashboard = () => {
  const { user } = useAuth();
  const [securityData, setSecurityData] = useState<SecurityData | null>(null);
  const [alerts, setAlerts] = useState<SecurityAlert[]>([]);
  const [auditLogs, setAuditLogs] = useState<AuditLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [timeWindow, setTimeWindow] = useState(24); // hours
  const [activeTab, setActiveTab] = useState<'overview' | 'alerts' | 'audit'>('overview');

  useEffect(() => {
    const fetchData = async () => {
      if (!user) return;

      try {
        setLoading(true);

        // Fetch security dashboard data
        const { data: secData, error: secError } = await getSecurityDashboard(timeWindow);
        if (secError) {
          console.error('Error fetching security data:', secError);
        } else {
          setSecurityData(secData);
        }

        // Fetch security alerts
        const { data: alertsData, error: alertsError } = await getSecurityAlerts(false); // Only unresolved
        if (alertsError) {
          console.error('Error fetching alerts:', alertsError);
        } else {
          setAlerts(alertsData || []);
        }

        // Fetch user audit logs
        const { data: auditData, error: auditError } = await getUserAuditLogs(user.id, 20);
        if (auditError) {
          console.error('Error fetching audit logs:', auditError);
        } else {
          setAuditLogs(auditData || []);
        }
      } catch (error) {
        console.error('Error fetching security dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [user, timeWindow]);

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'high':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatAction = (action: string) => {
    return action.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="text-center py-8">
          <Shield className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>Loading security data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Shield className="h-6 w-6" />
            Security Dashboard
          </h2>
          <p className="text-muted-foreground">
            Monitor security events and audit trails
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant={timeWindow === 1 ? 'default' : 'outline'}
            size="sm"
            onClick={() => setTimeWindow(1)}
          >
            1h
          </Button>
          <Button
            variant={timeWindow === 24 ? 'default' : 'outline'}
            size="sm"
            onClick={() => setTimeWindow(24)}
          >
            24h
          </Button>
          <Button
            variant={timeWindow === 168 ? 'default' : 'outline'}
            size="sm"
            onClick={() => setTimeWindow(168)}
          >
            7d
          </Button>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="flex space-x-1 bg-muted p-1 rounded-lg">
        <Button
          variant={activeTab === 'overview' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setActiveTab('overview')}
          className="flex-1"
        >
          <Activity className="h-4 w-4 mr-2" />
          Overview
        </Button>
        <Button
          variant={activeTab === 'alerts' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setActiveTab('alerts')}
          className="flex-1"
        >
          <AlertTriangle className="h-4 w-4 mr-2" />
          Alerts ({alerts.length})
        </Button>
        <Button
          variant={activeTab === 'audit' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setActiveTab('audit')}
          className="flex-1"
        >
          <FileText className="h-4 w-4 mr-2" />
          Audit Log
        </Button>
      </div>

      {/* Overview Tab */}
      {activeTab === 'overview' && securityData && (
        <div className="space-y-6">
          {/* Security Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Activities</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{securityData.total_audit_entries}</div>
                <p className="text-xs text-muted-foreground">
                  Last {timeWindow} hour{timeWindow > 1 ? 's' : ''}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Users</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{securityData.unique_users}</div>
                <p className="text-xs text-muted-foreground">
                  Unique users with activity
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Security Alerts</CardTitle>
                <AlertTriangle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className={`text-2xl font-bold ${securityData.security_alerts > 0 ? 'text-red-600' : 'text-green-600'}`}>
                  {securityData.security_alerts}
                </div>
                <p className="text-xs text-muted-foreground">
                  Unresolved alerts
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Rate Limit Violations</CardTitle>
                <Lock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className={`text-2xl font-bold ${securityData.rate_limit_violations > 0 ? 'text-orange-600' : 'text-green-600'}`}>
                  {securityData.rate_limit_violations}
                </div>
                <p className="text-xs text-muted-foreground">
                  Blocked requests
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Top Actions */}
          {Object.keys(securityData.top_actions).length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Top Actions
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {Object.entries(securityData.top_actions).map(([action, count]) => (
                    <div key={action} className="flex items-center justify-between">
                      <span className="capitalize">{formatAction(action)}</span>
                      <div className="flex items-center gap-2">
                        <div className="w-32 bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-blue-600 h-2 rounded-full" 
                            style={{ 
                              width: `${(count / securityData.total_audit_entries) * 100}%` 
                            }}
                          />
                        </div>
                        <span className="text-sm font-medium w-8 text-right">{count}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Risk Distribution */}
          {Object.keys(securityData.risk_distribution).length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Eye className="h-5 w-5" />
                  Risk Distribution
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {Object.entries(securityData.risk_distribution).map(([level, count]) => (
                    <div key={level} className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Badge 
                          variant="outline" 
                          className={getSeverityColor(level)}
                        >
                          {level.charAt(0).toUpperCase() + level.slice(1)} Risk
                        </Badge>
                      </div>
                      <span className="text-sm font-medium">{count} activities</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {/* Alerts Tab */}
      {activeTab === 'alerts' && (
        <div className="space-y-4">
          {alerts.length === 0 ? (
            <Card>
              <CardContent className="text-center py-8">
                <Shield className="h-12 w-12 mx-auto mb-4 text-green-600" />
                <h3 className="text-lg font-medium mb-2">No Active Alerts</h3>
                <p className="text-muted-foreground">
                  All security alerts have been resolved or there are no current issues.
                </p>
              </CardContent>
            </Card>
          ) : (
            alerts.map((alert) => (
              <Card key={alert.id}>
                <CardContent className="p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h4 className="font-medium">{alert.title}</h4>
                        <Badge 
                          variant="outline" 
                          className={getSeverityColor(alert.severity)}
                        >
                          {alert.severity}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground mb-3">
                        {alert.description}
                      </p>
                      <div className="flex items-center gap-4 text-xs text-muted-foreground">
                        <span>Type: {alert.alert_type}</span>
                        {alert.source_ip && <span>IP: {alert.source_ip}</span>}
                        <span>{new Date(alert.created_at).toLocaleString()}</span>
                      </div>
                    </div>
                    <AlertTriangle className="h-5 w-5 text-orange-500 ml-4" />
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      )}

      {/* Audit Log Tab */}
      {activeTab === 'audit' && (
        <div className="space-y-4">
          {auditLogs.length === 0 ? (
            <Card>
              <CardContent className="text-center py-8">
                <FileText className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <h3 className="text-lg font-medium mb-2">No Audit Logs</h3>
                <p className="text-muted-foreground">
                  No account activities have been recorded yet.
                </p>
              </CardContent>
            </Card>
          ) : (
            auditLogs.map((log) => (
              <Card key={log.id}>
                <CardContent className="p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h4 className="font-medium">{formatAction(log.action)}</h4>
                        <Clock className="h-4 w-4 text-muted-foreground" />
                      </div>
                      {log.reason && (
                        <p className="text-sm text-muted-foreground mb-2">
                          Reason: {log.reason}
                        </p>
                      )}
                      {log.changed_fields && log.changed_fields.length > 0 && (
                        <div className="mb-2">
                          <span className="text-xs text-muted-foreground">Changed fields: </span>
                          {log.changed_fields.map((field, index) => (
                            <Badge key={field} variant="outline" className="text-xs mr-1">
                              {field}
                            </Badge>
                          ))}
                        </div>
                      )}
                      <div className="flex items-center gap-4 text-xs text-muted-foreground">
                        {log.ip_address && <span>IP: {log.ip_address}</span>}
                        <span>{new Date(log.created_at).toLocaleString()}</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      )}
    </div>
  );
};

export default SecurityDashboard;
