import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { 
  Heart, 
  DollarSign, 
  PiggyBank, 
  TrendingUp, 
  Calendar,
  Target,
  Award,
  BarChart3
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { calculateImpactMetrics } from '@/lib/database';

interface ImpactData {
  total_transactions: number;
  donated_amount: number;
  saved_amount: number;
  efficiency_score: number;
}

export const ImpactMetrics = () => {
  const { user } = useAuth();
  const [impactData, setImpactData] = useState<ImpactData | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState<'week' | 'month' | 'quarter' | 'year'>('month');

  useEffect(() => {
    const fetchImpactData = async () => {
      if (!user) return;

      try {
        setLoading(true);
        
        // Calculate date range
        const endDate = new Date();
        const startDate = new Date();
        
        switch (timeRange) {
          case 'week':
            startDate.setDate(endDate.getDate() - 7);
            break;
          case 'month':
            startDate.setMonth(endDate.getMonth() - 1);
            break;
          case 'quarter':
            startDate.setMonth(endDate.getMonth() - 3);
            break;
          case 'year':
            startDate.setFullYear(endDate.getFullYear() - 1);
            break;
        }

        const { data, error } = await calculateImpactMetrics(
          user.id,
          startDate.toISOString(),
          endDate.toISOString()
        );
        
        if (error) {
          console.error('Error fetching impact data:', error);
        } else {
          setImpactData(data);
        }
      } catch (error) {
        console.error('Error fetching impact data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchImpactData();
  }, [user, timeRange]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const getEfficiencyColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-blue-600';
    if (score >= 40) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getEfficiencyMessage = (score: number) => {
    if (score >= 80) return 'Excellent donation efficiency!';
    if (score >= 60) return 'Good donation efficiency';
    if (score >= 40) return 'Room for improvement';
    return 'Consider resuming paused accounts';
  };

  const calculatePotentialImpact = () => {
    if (!impactData) return 0;
    return impactData.donated_amount + impactData.saved_amount;
  };

  const getTimeRangeLabel = () => {
    switch (timeRange) {
      case 'week': return 'Last 7 days';
      case 'month': return 'Last 30 days';
      case 'quarter': return 'Last 3 months';
      case 'year': return 'Last 12 months';
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="text-center py-8">
          <BarChart3 className="h-8 w-8 animate-pulse mx-auto mb-4" />
          <p>Loading impact metrics...</p>
        </div>
      </div>
    );
  }

  if (!impactData) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <BarChart3 className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
          <h3 className="text-lg font-medium mb-2">No impact data available</h3>
          <p className="text-muted-foreground">
            Start making transactions to see your donation impact.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <Heart className="h-5 w-5" />
            Your Impact
          </h3>
          <p className="text-sm text-muted-foreground">
            Track your donation progress and savings
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant={timeRange === 'week' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setTimeRange('week')}
          >
            Week
          </Button>
          <Button
            variant={timeRange === 'month' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setTimeRange('month')}
          >
            Month
          </Button>
          <Button
            variant={timeRange === 'quarter' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setTimeRange('quarter')}
          >
            Quarter
          </Button>
          <Button
            variant={timeRange === 'year' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setTimeRange('year')}
          >
            Year
          </Button>
        </div>
      </div>

      {/* Time Range Indicator */}
      <div className="text-center">
        <p className="text-sm text-muted-foreground">{getTimeRangeLabel()}</p>
      </div>

      {/* Impact Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Amount Donated</CardTitle>
            <Heart className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {formatCurrency(impactData.donated_amount)}
            </div>
            <p className="text-xs text-muted-foreground">
              From active accounts
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Amount Saved</CardTitle>
            <PiggyBank className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {formatCurrency(impactData.saved_amount)}
            </div>
            <p className="text-xs text-muted-foreground">
              From paused accounts
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Potential</CardTitle>
            <Target className="h-4 w-4 text-purple-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">
              {formatCurrency(calculatePotentialImpact())}
            </div>
            <p className="text-xs text-muted-foreground">
              If all accounts were active
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Efficiency Score</CardTitle>
            <Award className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getEfficiencyColor(impactData.efficiency_score)}`}>
              {impactData.efficiency_score.toFixed(1)}%
            </div>
            <p className="text-xs text-muted-foreground">
              Donation efficiency
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Efficiency Progress */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Donation Efficiency
          </CardTitle>
          <CardDescription>
            {getEfficiencyMessage(impactData.efficiency_score)}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex justify-between text-sm">
              <span>Current Efficiency</span>
              <span className={getEfficiencyColor(impactData.efficiency_score)}>
                {impactData.efficiency_score.toFixed(1)}%
              </span>
            </div>
            <Progress 
              value={impactData.efficiency_score} 
              className="h-3"
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>0%</span>
              <span>50%</span>
              <span>100%</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            Impact Breakdown
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
              <div className="flex items-center gap-3">
                <Heart className="h-5 w-5 text-green-600" />
                <div>
                  <p className="font-medium text-green-900">Donated</p>
                  <p className="text-sm text-green-700">Money sent to charities</p>
                </div>
              </div>
              <div className="text-right">
                <p className="font-bold text-green-900">
                  {formatCurrency(impactData.donated_amount)}
                </p>
                <p className="text-sm text-green-700">
                  {calculatePotentialImpact() > 0 
                    ? `${((impactData.donated_amount / calculatePotentialImpact()) * 100).toFixed(1)}%`
                    : '0%'
                  } of potential
                </p>
              </div>
            </div>

            <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
              <div className="flex items-center gap-3">
                <PiggyBank className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="font-medium text-blue-900">Saved</p>
                  <p className="text-sm text-blue-700">Money kept due to paused accounts</p>
                </div>
              </div>
              <div className="text-right">
                <p className="font-bold text-blue-900">
                  {formatCurrency(impactData.saved_amount)}
                </p>
                <p className="text-sm text-blue-700">
                  {calculatePotentialImpact() > 0 
                    ? `${((impactData.saved_amount / calculatePotentialImpact()) * 100).toFixed(1)}%`
                    : '0%'
                  } of potential
                </p>
              </div>
            </div>

            {impactData.saved_amount > 0 && (
              <div className="p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                <div className="flex items-start gap-3">
                  <TrendingUp className="h-5 w-5 text-yellow-600 mt-0.5" />
                  <div>
                    <p className="font-medium text-yellow-900">Optimization Opportunity</p>
                    <p className="text-sm text-yellow-700">
                      You could donate an additional {formatCurrency(impactData.saved_amount)} 
                      by resuming paused accounts. Consider reviewing your account settings.
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Milestones */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Award className="h-5 w-5" />
            Milestones
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm">First Donation</span>
              <span className="text-sm font-medium text-green-600">
                {impactData.donated_amount > 0 ? '✓ Achieved' : 'Pending'}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">$10 Donated</span>
              <span className="text-sm font-medium text-green-600">
                {impactData.donated_amount >= 10 ? '✓ Achieved' : `$${(10 - impactData.donated_amount).toFixed(2)} to go`}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">$50 Donated</span>
              <span className="text-sm font-medium text-green-600">
                {impactData.donated_amount >= 50 ? '✓ Achieved' : `$${(50 - impactData.donated_amount).toFixed(2)} to go`}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">80% Efficiency</span>
              <span className="text-sm font-medium text-blue-600">
                {impactData.efficiency_score >= 80 ? '✓ Achieved' : `${(80 - impactData.efficiency_score).toFixed(1)}% to go`}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ImpactMetrics;
