/**
 * Unit Tests for Pause History and Reason Tracking
 * Tests the history logging and reason collection functionality
 */

// Mock pause history data
const mockPauseHistory = [
  {
    id: 'history-1',
    account_id: 'account-1',
    account_name: 'Main Checking',
    action: 'pause',
    reason: 'Going on vacation',
    previous_state: true,
    new_state: false,
    scheduled_start: null,
    scheduled_end: null,
    triggered_by: 'user',
    created_at: '2024-01-15T10:30:00Z',
    metadata: { account_name: 'Main Checking' }
  },
  {
    id: 'history-2',
    account_id: 'account-1',
    account_name: 'Main Checking',
    action: 'schedule',
    reason: 'Holiday break',
    previous_state: true,
    new_state: true,
    scheduled_start: '2024-12-20T00:00:00Z',
    scheduled_end: '2025-01-05T00:00:00Z',
    triggered_by: 'user',
    created_at: '2024-01-10T14:20:00Z',
    metadata: { account_name: 'Main Checking' }
  },
  {
    id: 'history-3',
    account_id: 'account-2',
    account_name: 'Savings Account',
    action: 'pause',
    reason: 'Automatically paused by schedule',
    previous_state: true,
    new_state: false,
    scheduled_start: '2024-12-20T00:00:00Z',
    scheduled_end: '2025-01-05T00:00:00Z',
    triggered_by: 'scheduled',
    created_at: '2024-12-20T00:00:00Z',
    metadata: { account_name: 'Savings Account', auto_paused_changed: true }
  },
  {
    id: 'history-4',
    account_id: 'account-2',
    account_name: 'Savings Account',
    action: 'resume',
    reason: 'Automatically resumed after schedule',
    previous_state: false,
    new_state: true,
    scheduled_start: null,
    scheduled_end: null,
    triggered_by: 'scheduled',
    created_at: '2025-01-05T00:00:00Z',
    metadata: { account_name: 'Savings Account', auto_paused_changed: true }
  }
];

describe('Pause History and Reason Tracking', () => {
  describe('History Entry Creation', () => {
    test('should create proper history entry for manual pause', () => {
      const createHistoryEntry = (accountId, action, reason, triggeredBy = 'user') => {
        return {
          id: `history-${Date.now()}`,
          account_id: accountId,
          action,
          reason,
          triggered_by: triggeredBy,
          created_at: new Date().toISOString(),
          previous_state: action === 'pause' ? true : false,
          new_state: action === 'pause' ? false : true
        };
      };

      const entry = createHistoryEntry('account-1', 'pause', 'Budget constraints');
      expect(entry.action).toBe('pause');
      expect(entry.reason).toBe('Budget constraints');
      expect(entry.triggered_by).toBe('user');
      expect(entry.previous_state).toBe(true);
      expect(entry.new_state).toBe(false);
    });

    test('should create proper history entry for scheduled action', () => {
      const createScheduledEntry = (accountId, action, reason) => {
        return {
          id: `history-${Date.now()}`,
          account_id: accountId,
          action,
          reason,
          triggered_by: 'scheduled',
          created_at: new Date().toISOString(),
          scheduled_start: '2024-12-20T00:00:00Z',
          scheduled_end: '2025-01-05T00:00:00Z'
        };
      };

      const entry = createScheduledEntry('account-1', 'schedule', 'Vacation mode');
      expect(entry.action).toBe('schedule');
      expect(entry.triggered_by).toBe('scheduled');
      expect(entry.scheduled_start).toBeTruthy();
      expect(entry.scheduled_end).toBeTruthy();
    });
  });

  describe('Reason Collection', () => {
    test('should provide default reasons for different actions', () => {
      const getDefaultReason = (action, isAutomatic = false) => {
        if (isAutomatic) {
          switch (action) {
            case 'pause':
              return 'Automatically paused by schedule';
            case 'resume':
              return 'Automatically resumed after schedule';
            default:
              return 'Automatic action';
          }
        }

        switch (action) {
          case 'pause':
            return 'Manual pause';
          case 'resume':
            return 'Manual resume';
          case 'schedule':
            return 'Scheduled pause';
          case 'cancel_schedule':
            return 'Schedule cancelled';
          default:
            return 'User action';
        }
      };

      expect(getDefaultReason('pause', false)).toBe('Manual pause');
      expect(getDefaultReason('pause', true)).toBe('Automatically paused by schedule');
      expect(getDefaultReason('resume', false)).toBe('Manual resume');
      expect(getDefaultReason('schedule', false)).toBe('Scheduled pause');
    });

    test('should suggest common reasons for user actions', () => {
      const getCommonReasons = (action) => {
        switch (action) {
          case 'pause':
            return [
              'Vacation/travel',
              'Budget constraints',
              'Temporary break',
              'Account maintenance',
              'Emergency expenses'
            ];
          case 'resume':
            return [
              'Back from vacation',
              'Budget improved',
              'Ready to donate again',
              'Account issue resolved',
              'New year resolution'
            ];
          case 'schedule':
            return [
              'Holiday vacation',
              'Business trip',
              'Budget planning',
              'Temporary pause needed',
              'Seasonal break'
            ];
          default:
            return [];
        }
      };

      const pauseReasons = getCommonReasons('pause');
      expect(pauseReasons).toContain('Vacation/travel');
      expect(pauseReasons).toContain('Budget constraints');

      const resumeReasons = getCommonReasons('resume');
      expect(resumeReasons).toContain('Back from vacation');
      expect(resumeReasons).toContain('Budget improved');
    });
  });

  describe('History Filtering and Sorting', () => {
    test('should filter history by action type', () => {
      const filterByAction = (history, action) => {
        return history.filter(entry => entry.action === action);
      };

      const pauseActions = filterByAction(mockPauseHistory, 'pause');
      expect(pauseActions).toHaveLength(2);
      expect(pauseActions.every(entry => entry.action === 'pause')).toBe(true);

      const scheduleActions = filterByAction(mockPauseHistory, 'schedule');
      expect(scheduleActions).toHaveLength(1);
    });

    test('should filter history by trigger type', () => {
      const filterByTrigger = (history, triggeredBy) => {
        return history.filter(entry => entry.triggered_by === triggeredBy);
      };

      const userActions = filterByTrigger(mockPauseHistory, 'user');
      expect(userActions).toHaveLength(2);

      const scheduledActions = filterByTrigger(mockPauseHistory, 'scheduled');
      expect(scheduledActions).toHaveLength(2);
    });

    test('should sort history by date', () => {
      const sortByDate = (history, ascending = false) => {
        return [...history].sort((a, b) => {
          const dateA = new Date(a.created_at);
          const dateB = new Date(b.created_at);
          return ascending ? dateA.getTime() - dateB.getTime() : dateB.getTime() - dateA.getTime();
        });
      };

      const sortedDesc = sortByDate(mockPauseHistory, false);
      expect(new Date(sortedDesc[0].created_at).getTime()).toBeGreaterThan(
        new Date(sortedDesc[1].created_at).getTime()
      );

      const sortedAsc = sortByDate(mockPauseHistory, true);
      expect(new Date(sortedAsc[0].created_at).getTime()).toBeLessThan(
        new Date(sortedAsc[1].created_at).getTime()
      );
    });

    test('should filter history by account', () => {
      const filterByAccount = (history, accountId) => {
        return history.filter(entry => entry.account_id === accountId);
      };

      const account1History = filterByAccount(mockPauseHistory, 'account-1');
      expect(account1History).toHaveLength(2);
      expect(account1History.every(entry => entry.account_id === 'account-1')).toBe(true);

      const account2History = filterByAccount(mockPauseHistory, 'account-2');
      expect(account2History).toHaveLength(2);
    });
  });

  describe('History Display Formatting', () => {
    test('should format action names for display', () => {
      const formatAction = (action) => {
        switch (action) {
          case 'pause':
            return 'Paused';
          case 'resume':
            return 'Resumed';
          case 'schedule':
            return 'Scheduled';
          case 'cancel_schedule':
            return 'Cancelled Schedule';
          default:
            return action;
        }
      };

      expect(formatAction('pause')).toBe('Paused');
      expect(formatAction('resume')).toBe('Resumed');
      expect(formatAction('schedule')).toBe('Scheduled');
      expect(formatAction('cancel_schedule')).toBe('Cancelled Schedule');
    });

    test('should format dates for display', () => {
      const formatDate = (dateString) => {
        return new Date(dateString).toLocaleString();
      };

      const formatted = formatDate('2024-01-15T10:30:00Z');
      expect(formatted).toContain('2024');
      expect(typeof formatted).toBe('string');
    });

    test('should format trigger source for display', () => {
      const formatTrigger = (triggeredBy) => {
        switch (triggeredBy) {
          case 'user':
            return 'Manual';
          case 'scheduled':
            return 'Automatic';
          case 'system':
            return 'System';
          default:
            return 'Unknown';
        }
      };

      expect(formatTrigger('user')).toBe('Manual');
      expect(formatTrigger('scheduled')).toBe('Automatic');
      expect(formatTrigger('system')).toBe('System');
    });
  });

  describe('History Analytics', () => {
    test('should calculate pause/resume statistics', () => {
      const calculateStats = (history) => {
        const stats = {
          total_actions: history.length,
          pauses: 0,
          resumes: 0,
          schedules: 0,
          manual_actions: 0,
          automatic_actions: 0,
          accounts_affected: new Set()
        };

        history.forEach(entry => {
          switch (entry.action) {
            case 'pause':
              stats.pauses++;
              break;
            case 'resume':
              stats.resumes++;
              break;
            case 'schedule':
              stats.schedules++;
              break;
          }

          if (entry.triggered_by === 'user') {
            stats.manual_actions++;
          } else {
            stats.automatic_actions++;
          }

          stats.accounts_affected.add(entry.account_id);
        });

        return {
          ...stats,
          accounts_affected: stats.accounts_affected.size
        };
      };

      const stats = calculateStats(mockPauseHistory);
      expect(stats.total_actions).toBe(4);
      expect(stats.pauses).toBe(2);
      expect(stats.resumes).toBe(1);
      expect(stats.schedules).toBe(1);
      expect(stats.manual_actions).toBe(2);
      expect(stats.automatic_actions).toBe(2);
      expect(stats.accounts_affected).toBe(2);
    });

    test('should identify most common reasons', () => {
      const getMostCommonReasons = (history, limit = 3) => {
        const reasonCounts = {};
        
        history.forEach(entry => {
          if (entry.reason) {
            reasonCounts[entry.reason] = (reasonCounts[entry.reason] || 0) + 1;
          }
        });

        return Object.entries(reasonCounts)
          .sort(([,a], [,b]) => b - a)
          .slice(0, limit)
          .map(([reason, count]) => ({ reason, count }));
      };

      const commonReasons = getMostCommonReasons(mockPauseHistory);
      expect(commonReasons.length).toBeGreaterThan(0); // Should have at least some reasons
      expect(commonReasons[0]).toHaveProperty('reason');
      expect(commonReasons[0]).toHaveProperty('count');
    });
  });

  describe('History Validation', () => {
    test('should validate history entry structure', () => {
      const validateHistoryEntry = (entry) => {
        const required = ['id', 'account_id', 'action', 'triggered_by', 'created_at'];
        const validActions = ['pause', 'resume', 'schedule', 'cancel_schedule'];
        const validTriggers = ['user', 'system', 'scheduled'];

        const errors = [];

        required.forEach(field => {
          if (!entry[field]) {
            errors.push(`Missing required field: ${field}`);
          }
        });

        if (!validActions.includes(entry.action)) {
          errors.push(`Invalid action: ${entry.action}`);
        }

        if (!validTriggers.includes(entry.triggered_by)) {
          errors.push(`Invalid trigger: ${entry.triggered_by}`);
        }

        return {
          valid: errors.length === 0,
          errors
        };
      };

      // Valid entry
      const validEntry = mockPauseHistory[0];
      const validResult = validateHistoryEntry(validEntry);
      expect(validResult.valid).toBe(true);
      expect(validResult.errors).toHaveLength(0);

      // Invalid entry
      const invalidEntry = { id: 'test', action: 'invalid' };
      const invalidResult = validateHistoryEntry(invalidEntry);
      expect(invalidResult.valid).toBe(false);
      expect(invalidResult.errors.length).toBeGreaterThan(0);
    });
  });
});

// Export for use in other test files
module.exports = {
  mockPauseHistory
};
