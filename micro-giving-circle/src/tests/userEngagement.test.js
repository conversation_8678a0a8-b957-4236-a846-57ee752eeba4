/**
 * Unit Tests for User Engagement Features
 * Tests smart suggestions, re-engagement campaigns, and impact metrics
 */

// Mock engagement data
const mockSmartSuggestions = [
  {
    id: 'suggestion-1',
    user_id: 'user-123',
    suggestion_type: 'pause_for_vacation',
    title: 'Upcoming Vacation Pause Reminder',
    description: 'You have scheduled pauses coming up. Would you like to review or modify them?',
    confidence_score: 85,
    reasoning: { trigger: 'scheduled_pause_approaching' },
    suggested_action: { action: 'review_scheduled_pauses' },
    priority: 'medium',
    status: 'active',
    expires_at: '2024-02-15T00:00:00Z',
    created_at: '2024-01-15T10:30:00Z'
  },
  {
    id: 'suggestion-2',
    user_id: 'user-123',
    suggestion_type: 'resume_reminder',
    title: 'Consider Resuming Paused Accounts',
    description: 'You have 2 account(s) paused for an average of 45 days. Ready to resume donations?',
    confidence_score: 70,
    reasoning: { 
      trigger: 'long_pause_detected',
      paused_accounts: 2,
      avg_pause_days: 45
    },
    suggested_action: { action: 'review_paused_accounts' },
    priority: 'medium',
    status: 'active',
    expires_at: '2024-02-01T00:00:00Z',
    created_at: '2024-01-15T10:35:00Z'
  }
];

const mockReengagementCampaigns = [
  {
    id: 'campaign-1',
    user_id: 'user-123',
    campaign_type: 'long_pause_reminder',
    title: 'Your donations are paused - ready to resume?',
    message: 'You\'ve had accounts paused for a while. Every small donation makes a difference!',
    call_to_action: 'Review Paused Accounts',
    delivery_channel: 'in_app',
    status: 'scheduled',
    scheduled_for: '2024-01-16T10:00:00Z',
    created_at: '2024-01-15T10:30:00Z'
  },
  {
    id: 'campaign-2',
    user_id: 'user-456',
    campaign_type: 'impact_showcase',
    title: 'See your donation impact!',
    message: 'Check out how much you\'ve donated and the difference you\'ve made.',
    call_to_action: 'View Impact Report',
    delivery_channel: 'in_app',
    status: 'delivered',
    scheduled_for: '2024-01-14T15:00:00Z',
    delivered_at: '2024-01-14T15:00:00Z',
    created_at: '2024-01-11T10:30:00Z'
  }
];

const mockImpactMetrics = {
  total_transactions: 150,
  donated_amount: 75.50,
  saved_amount: 25.25,
  efficiency_score: 75.0
};

const mockEngagementMetrics = [
  {
    id: 'metric-1',
    user_id: 'user-123',
    metric_type: 'suggestion_accepted',
    metric_value: 85,
    context_data: {
      suggestion_type: 'pause_for_vacation',
      suggestion_id: 'suggestion-1'
    },
    triggered_at: '2024-01-15T10:30:00Z'
  },
  {
    id: 'metric-2',
    user_id: 'user-123',
    metric_type: 'suggestion_dismissed',
    metric_value: 70,
    context_data: {
      suggestion_type: 'resume_reminder',
      suggestion_id: 'suggestion-2'
    },
    triggered_at: '2024-01-15T11:00:00Z'
  }
];

describe('User Engagement Features', () => {
  describe('Smart Suggestions', () => {
    test('should generate vacation pause suggestions', () => {
      const generateVacationSuggestion = (userAccounts) => {
        const hasUpcomingScheduledPause = userAccounts.some(account => 
          account.scheduled_pause_start && 
          new Date(account.scheduled_pause_start) <= new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) &&
          new Date(account.scheduled_pause_start) > new Date()
        );

        if (hasUpcomingScheduledPause) {
          return {
            suggestion_type: 'pause_for_vacation',
            title: 'Upcoming Vacation Pause Reminder',
            description: 'You have scheduled pauses coming up. Would you like to review or modify them?',
            confidence_score: 85,
            priority: 'medium'
          };
        }

        return null;
      };

      const accountsWithScheduledPause = [
        {
          id: 'account-1',
          scheduled_pause_start: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString() // 3 days from now
        }
      ];

      const suggestion = generateVacationSuggestion(accountsWithScheduledPause);
      expect(suggestion).not.toBeNull();
      expect(suggestion.suggestion_type).toBe('pause_for_vacation');
      expect(suggestion.confidence_score).toBe(85);
    });

    test('should generate resume reminders for long-paused accounts', () => {
      const generateResumeSuggestion = (userAccounts) => {
        const pausedAccounts = userAccounts.filter(account => !account.rounding_enabled);
        
        if (pausedAccounts.length === 0) return null;

        const avgPauseDays = pausedAccounts.reduce((sum, account) => {
          const daysPaused = (Date.now() - new Date(account.updated_at).getTime()) / (1000 * 60 * 60 * 24);
          return sum + daysPaused;
        }, 0) / pausedAccounts.length;

        if (avgPauseDays > 30) {
          return {
            suggestion_type: 'resume_reminder',
            title: 'Consider Resuming Paused Accounts',
            description: `You have ${pausedAccounts.length} account(s) paused for an average of ${Math.round(avgPauseDays)} days. Ready to resume donations?`,
            confidence_score: 70,
            priority: 'medium'
          };
        }

        return null;
      };

      const accountsWithLongPause = [
        {
          id: 'account-1',
          rounding_enabled: false,
          updated_at: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000).toISOString() // 45 days ago
        },
        {
          id: 'account-2',
          rounding_enabled: false,
          updated_at: new Date(Date.now() - 35 * 24 * 60 * 60 * 1000).toISOString() // 35 days ago
        }
      ];

      const suggestion = generateResumeSuggestion(accountsWithLongPause);
      expect(suggestion).not.toBeNull();
      expect(suggestion.suggestion_type).toBe('resume_reminder');
      expect(suggestion.description).toContain('2 account(s)');
      expect(suggestion.description).toContain('40 days'); // Average of 45 and 35
    });

    test('should generate optimization suggestions for mixed account states', () => {
      const generateOptimizationSuggestion = (userAccounts) => {
        const activeAccounts = userAccounts.filter(account => account.rounding_enabled);
        const pausedAccounts = userAccounts.filter(account => !account.rounding_enabled);

        if (activeAccounts.length > 0 && pausedAccounts.length > 0) {
          return {
            suggestion_type: 'optimize_donations',
            title: 'Optimize Your Donation Strategy',
            description: `You have ${activeAccounts.length} active and ${pausedAccounts.length} paused accounts. Review your setup to maximize impact.`,
            confidence_score: 60,
            priority: 'low'
          };
        }

        return null;
      };

      const mixedAccounts = [
        { id: 'account-1', rounding_enabled: true },
        { id: 'account-2', rounding_enabled: false },
        { id: 'account-3', rounding_enabled: true }
      ];

      const suggestion = generateOptimizationSuggestion(mixedAccounts);
      expect(suggestion).not.toBeNull();
      expect(suggestion.suggestion_type).toBe('optimize_donations');
      expect(suggestion.description).toContain('2 active and 1 paused');
    });

    test('should calculate suggestion confidence scores', () => {
      const calculateConfidenceScore = (suggestionType, context) => {
        switch (suggestionType) {
          case 'pause_for_vacation':
            return context.daysUntilScheduledPause <= 7 ? 85 : 60;
          
          case 'resume_reminder':
            const avgPauseDays = context.avgPauseDays || 0;
            if (avgPauseDays > 60) return 90;
            if (avgPauseDays > 30) return 70;
            return 40;
          
          case 'optimize_donations':
            const ratio = Math.min(context.activeAccounts, context.pausedAccounts) / 
                         Math.max(context.activeAccounts, context.pausedAccounts);
            return Math.round(ratio * 80); // Higher score for more balanced accounts
          
          default:
            return 50;
        }
      };

      expect(calculateConfidenceScore('pause_for_vacation', { daysUntilScheduledPause: 3 })).toBe(85);
      expect(calculateConfidenceScore('resume_reminder', { avgPauseDays: 45 })).toBe(70);
      expect(calculateConfidenceScore('optimize_donations', { activeAccounts: 2, pausedAccounts: 2 })).toBe(80);
    });
  });

  describe('Re-engagement Campaigns', () => {
    test('should create appropriate campaign content', () => {
      const createCampaignContent = (campaignType) => {
        const campaigns = {
          'long_pause_reminder': {
            title: 'Your donations are paused - ready to resume?',
            message: 'You\'ve had accounts paused for a while. Every small donation makes a difference!',
            call_to_action: 'Review Paused Accounts',
            schedule_delay_hours: 24
          },
          'impact_showcase': {
            title: 'See your donation impact!',
            message: 'Check out how much you\'ve donated and the difference you\'ve made.',
            call_to_action: 'View Impact Report',
            schedule_delay_hours: 72
          },
          'feature_highlight': {
            title: 'New features to help you donate smarter',
            message: 'Discover new ways to manage your round-up donations effectively.',
            call_to_action: 'Explore Features',
            schedule_delay_hours: 168
          }
        };

        return campaigns[campaignType] || null;
      };

      const longPauseCampaign = createCampaignContent('long_pause_reminder');
      expect(longPauseCampaign.title).toContain('paused');
      expect(longPauseCampaign.schedule_delay_hours).toBe(24);

      const impactCampaign = createCampaignContent('impact_showcase');
      expect(impactCampaign.title).toContain('impact');
      expect(impactCampaign.schedule_delay_hours).toBe(72);
    });

    test('should personalize campaign messages', () => {
      const personalizeCampaign = (campaignType, userData) => {
        const baseContent = {
          'long_pause_reminder': {
            title: 'Your donations are paused - ready to resume?',
            message: 'You\'ve had accounts paused for a while. Every small donation makes a difference!'
          }
        };

        const content = baseContent[campaignType];
        if (!content) return null;

        // Personalize based on user data
        if (userData.pausedAccounts > 1) {
          content.message = `You have ${userData.pausedAccounts} accounts paused. Every small donation makes a difference!`;
        }

        if (userData.avgPauseDays > 60) {
          content.message += ' It\'s been over 2 months - consider resuming to maximize your impact.';
        }

        return content;
      };

      const userData = { pausedAccounts: 3, avgPauseDays: 75 };
      const personalizedCampaign = personalizeCampaign('long_pause_reminder', userData);
      
      expect(personalizedCampaign.message).toContain('3 accounts paused');
      expect(personalizedCampaign.message).toContain('over 2 months');
    });

    test('should track campaign engagement', () => {
      const trackCampaignEngagement = (campaign, action, timestamp) => {
        const engagement = {
          campaign_id: campaign.id,
          action,
          timestamp,
          user_id: campaign.user_id
        };

        // Update campaign status based on action
        switch (action) {
          case 'delivered':
            campaign.status = 'delivered';
            campaign.delivered_at = timestamp;
            break;
          case 'opened':
            campaign.status = 'opened';
            campaign.opened_at = timestamp;
            break;
          case 'clicked':
            campaign.status = 'clicked';
            campaign.clicked_at = timestamp;
            break;
        }

        return engagement;
      };

      const campaign = { ...mockReengagementCampaigns[0] };
      const timestamp = '2024-01-16T10:00:00Z';
      
      const deliveredEngagement = trackCampaignEngagement(campaign, 'delivered', timestamp);
      expect(deliveredEngagement.action).toBe('delivered');
      expect(campaign.status).toBe('delivered');
      expect(campaign.delivered_at).toBe(timestamp);
    });
  });

  describe('Impact Metrics', () => {
    test('should calculate donation efficiency correctly', () => {
      const calculateEfficiency = (donatedAmount, savedAmount) => {
        const totalPotential = donatedAmount + savedAmount;
        return totalPotential > 0 ? (donatedAmount / totalPotential) * 100 : 0;
      };

      expect(calculateEfficiency(75, 25)).toBe(75); // 75% efficiency
      expect(calculateEfficiency(50, 50)).toBe(50); // 50% efficiency
      expect(calculateEfficiency(0, 0)).toBe(0);    // No activity
      expect(calculateEfficiency(100, 0)).toBe(100); // Perfect efficiency
    });

    test('should categorize efficiency scores', () => {
      const categorizeEfficiency = (score) => {
        if (score >= 80) return { level: 'excellent', message: 'Excellent donation efficiency!' };
        if (score >= 60) return { level: 'good', message: 'Good donation efficiency' };
        if (score >= 40) return { level: 'fair', message: 'Room for improvement' };
        return { level: 'poor', message: 'Consider resuming paused accounts' };
      };

      expect(categorizeEfficiency(85).level).toBe('excellent');
      expect(categorizeEfficiency(65).level).toBe('good');
      expect(categorizeEfficiency(45).level).toBe('fair');
      expect(categorizeEfficiency(25).level).toBe('poor');
    });

    test('should calculate milestone progress', () => {
      const calculateMilestones = (donatedAmount, efficiencyScore) => {
        const milestones = [
          { name: 'First Donation', target: 0.01, achieved: donatedAmount > 0 },
          { name: '$10 Donated', target: 10, achieved: donatedAmount >= 10 },
          { name: '$50 Donated', target: 50, achieved: donatedAmount >= 50 },
          { name: '$100 Donated', target: 100, achieved: donatedAmount >= 100 },
          { name: '80% Efficiency', target: 80, achieved: efficiencyScore >= 80 }
        ];

        return milestones.map(milestone => ({
          ...milestone,
          progress: milestone.name.includes('Efficiency') 
            ? Math.min(efficiencyScore / milestone.target * 100, 100)
            : Math.min(donatedAmount / milestone.target * 100, 100)
        }));
      };

      const milestones = calculateMilestones(75.50, 75);
      
      expect(milestones[0].achieved).toBe(true); // First donation
      expect(milestones[1].achieved).toBe(true); // $10 donated
      expect(milestones[2].achieved).toBe(true); // $50 donated
      expect(milestones[3].achieved).toBe(false); // $100 donated
      expect(milestones[4].achieved).toBe(false); // 80% efficiency
      
      expect(milestones[3].progress).toBeCloseTo(75.5); // 75.5% progress to $100
      expect(milestones[4].progress).toBeCloseTo(93.75); // 93.75% progress to 80% efficiency
    });

    test('should format currency correctly', () => {
      const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD',
        }).format(amount);
      };

      expect(formatCurrency(75.50)).toBe('$75.50');
      expect(formatCurrency(0)).toBe('$0.00');
      expect(formatCurrency(1234.56)).toBe('$1,234.56');
    });

    test('should calculate time-based metrics', () => {
      const calculateTimeBasedMetrics = (transactions, timeRange) => {
        const now = Date.now();
        let startTime;

        switch (timeRange) {
          case 'week':
            startTime = now - (7 * 24 * 60 * 60 * 1000);
            break;
          case 'month':
            startTime = now - (30 * 24 * 60 * 60 * 1000);
            break;
          case 'quarter':
            startTime = now - (90 * 24 * 60 * 60 * 1000);
            break;
          case 'year':
            startTime = now - (365 * 24 * 60 * 60 * 1000);
            break;
          default:
            startTime = 0;
        }

        const filteredTransactions = transactions.filter(tx => 
          new Date(tx.created_at).getTime() >= startTime
        );

        return {
          total_transactions: filteredTransactions.length,
          donated_amount: filteredTransactions
            .filter(tx => tx.account_active)
            .reduce((sum, tx) => sum + tx.round_up_amount, 0),
          saved_amount: filteredTransactions
            .filter(tx => !tx.account_active)
            .reduce((sum, tx) => sum + tx.round_up_amount, 0)
        };
      };

      const mockTransactions = [
        {
          created_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(), // 5 days ago
          round_up_amount: 2.50,
          account_active: true
        },
        {
          created_at: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000).toISOString(), // 45 days ago
          round_up_amount: 1.75,
          account_active: false
        }
      ];

      const weekMetrics = calculateTimeBasedMetrics(mockTransactions, 'week');
      expect(weekMetrics.total_transactions).toBe(1);
      expect(weekMetrics.donated_amount).toBe(2.50);

      const monthMetrics = calculateTimeBasedMetrics(mockTransactions, 'month');
      expect(monthMetrics.total_transactions).toBe(1); // Only the recent one

      const quarterMetrics = calculateTimeBasedMetrics(mockTransactions, 'quarter');
      expect(quarterMetrics.total_transactions).toBe(2); // Both transactions
    });
  });

  describe('Engagement Analytics', () => {
    test('should track suggestion acceptance rates', () => {
      const calculateAcceptanceRate = (engagementMetrics) => {
        const suggestionMetrics = engagementMetrics.filter(metric => 
          metric.metric_type === 'suggestion_accepted' || metric.metric_type === 'suggestion_dismissed'
        );

        if (suggestionMetrics.length === 0) return 0;

        const acceptedCount = suggestionMetrics.filter(metric => 
          metric.metric_type === 'suggestion_accepted'
        ).length;

        return (acceptedCount / suggestionMetrics.length) * 100;
      };

      const acceptanceRate = calculateAcceptanceRate(mockEngagementMetrics);
      expect(acceptanceRate).toBe(50); // 1 accepted out of 2 total
    });

    test('should identify engagement patterns', () => {
      const identifyEngagementPatterns = (engagementMetrics) => {
        const patterns = {};

        // Group by metric type
        engagementMetrics.forEach(metric => {
          if (!patterns[metric.metric_type]) {
            patterns[metric.metric_type] = [];
          }
          patterns[metric.metric_type].push(metric);
        });

        // Analyze patterns
        const analysis = {};
        Object.keys(patterns).forEach(type => {
          analysis[type] = {
            count: patterns[type].length,
            avg_value: patterns[type].reduce((sum, m) => sum + (m.metric_value || 0), 0) / patterns[type].length,
            frequency: patterns[type].length / 30 // Assuming 30-day period
          };
        });

        return analysis;
      };

      const patterns = identifyEngagementPatterns(mockEngagementMetrics);
      expect(patterns.suggestion_accepted.count).toBe(1);
      expect(patterns.suggestion_dismissed.count).toBe(1);
      expect(patterns.suggestion_accepted.avg_value).toBe(85);
      expect(patterns.suggestion_dismissed.avg_value).toBe(70);
    });
  });
});

// Export for use in other test files
module.exports = {
  mockSmartSuggestions,
  mockReengagementCampaigns,
  mockImpactMetrics,
  mockEngagementMetrics
};
