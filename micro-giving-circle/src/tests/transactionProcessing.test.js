/**
 * Unit Tests for Transaction Processing Logic
 * Tests that paused accounts don't process round-ups
 */

// Mock the shouldProcessRoundUps function
const shouldProcessRoundUps = (account) => {
  return !!(
    account.is_active &&           // Account must be active
    account.rounding_enabled &&    // Rounding must be enabled
    !account.deleted_at            // Account must not be soft deleted
  );
};

// Mock account data
const mockActiveAccount = {
  id: 'active-account',
  user_id: 'test-user',
  account_name: 'Active Checking',
  is_active: true,
  rounding_enabled: true,
  deleted_at: null,
  deletion_reason: null,
  created_at: '2024-01-01T00:00:00Z'
};

const mockPausedAccount = {
  id: 'paused-account',
  user_id: 'test-user',
  account_name: 'Paused Checking',
  is_active: true,
  rounding_enabled: false,
  deleted_at: null,
  deletion_reason: null,
  created_at: '2024-01-01T00:00:00Z'
};

const mockDeletedAccount = {
  id: 'deleted-account',
  user_id: 'test-user',
  account_name: 'Deleted Checking',
  is_active: true,
  rounding_enabled: true,
  deleted_at: '2024-01-02T00:00:00Z',
  deletion_reason: 'User requested',
  created_at: '2024-01-01T00:00:00Z'
};

const mockInactiveAccount = {
  id: 'inactive-account',
  user_id: 'test-user',
  account_name: 'Inactive Checking',
  is_active: false,
  rounding_enabled: true,
  deleted_at: null,
  deletion_reason: null,
  created_at: '2024-01-01T00:00:00Z'
};

describe('Transaction Processing Logic', () => {
  describe('shouldProcessRoundUps', () => {
    test('should return true for active account with rounding enabled', () => {
      expect(shouldProcessRoundUps(mockActiveAccount)).toBe(true);
    });

    test('should return false for paused account', () => {
      expect(shouldProcessRoundUps(mockPausedAccount)).toBe(false);
    });

    test('should return false for deleted account', () => {
      expect(shouldProcessRoundUps(mockDeletedAccount)).toBe(false);
    });

    test('should return false for inactive account', () => {
      expect(shouldProcessRoundUps(mockInactiveAccount)).toBe(false);
    });

    test('should handle null/undefined values gracefully', () => {
      const accountWithNulls = {
        ...mockActiveAccount,
        rounding_enabled: null,
        deleted_at: null
      };
      expect(shouldProcessRoundUps(accountWithNulls)).toBe(false);
    });
  });

  describe('Transaction Processing Business Logic', () => {
    test('should filter accounts correctly for transaction processing', () => {
      const accounts = [
        mockActiveAccount,
        mockPausedAccount,
        mockDeletedAccount,
        mockInactiveAccount
      ];

      const processableAccounts = accounts.filter(shouldProcessRoundUps);
      
      expect(processableAccounts).toHaveLength(1);
      expect(processableAccounts[0].id).toBe('active-account');
    });

    test('should calculate round-ups only for processable accounts', () => {
      const mockTransactions = [
        { account_id: 'active-account', amount: 5.25 },
        { account_id: 'paused-account', amount: 10.75 },
        { account_id: 'deleted-account', amount: 15.50 }
      ];

      const accounts = [mockActiveAccount, mockPausedAccount, mockDeletedAccount];
      const accountMap = new Map(accounts.map(acc => [acc.id, acc]));

      const processedTransactions = mockTransactions
        .filter(transaction => {
          const account = accountMap.get(transaction.account_id);
          return account && shouldProcessRoundUps(account);
        })
        .map(transaction => ({
          ...transaction,
          round_up: Math.ceil(transaction.amount) - transaction.amount
        }));

      expect(processedTransactions).toHaveLength(1);
      expect(processedTransactions[0].account_id).toBe('active-account');
      expect(processedTransactions[0].round_up).toBe(0.75);
    });

    test('should handle empty account list', () => {
      const accounts = [];
      const processableAccounts = accounts.filter(shouldProcessRoundUps);
      expect(processableAccounts).toHaveLength(0);
    });

    test('should handle all paused accounts', () => {
      const accounts = [mockPausedAccount, mockDeletedAccount, mockInactiveAccount];
      const processableAccounts = accounts.filter(shouldProcessRoundUps);
      expect(processableAccounts).toHaveLength(0);
    });
  });

  describe('Account Status Summary Logic', () => {
    test('should categorize accounts correctly', () => {
      const accounts = [
        mockActiveAccount,
        mockPausedAccount,
        mockDeletedAccount,
        mockInactiveAccount
      ];

      const summary = {
        total: accounts.length,
        active: accounts.filter(shouldProcessRoundUps).length,
        paused: accounts.filter(acc => acc.is_active && !acc.rounding_enabled && !acc.deleted_at).length,
        inactive: accounts.filter(acc => !acc.is_active && !acc.deleted_at).length,
        deleted: accounts.filter(acc => acc.deleted_at).length
      };

      expect(summary.total).toBe(4);
      expect(summary.active).toBe(1);
      expect(summary.paused).toBe(1);
      expect(summary.inactive).toBe(1);
      expect(summary.deleted).toBe(1);
    });
  });

  describe('Round-up Calculation Logic', () => {
    test('should calculate round-ups correctly', () => {
      const calculateRoundUp = (amount) => Math.round((Math.ceil(amount) - amount) * 100) / 100;

      expect(calculateRoundUp(5.25)).toBe(0.75);
      expect(calculateRoundUp(10.00)).toBe(0.00);
      expect(calculateRoundUp(7.99)).toBe(0.01);
      expect(calculateRoundUp(15.50)).toBe(0.50);
    });

    test('should only process round-ups for eligible accounts', () => {
      const processTransaction = (transaction, account) => {
        if (!shouldProcessRoundUps(account)) {
          return null; // Skip processing
        }

        const roundUp = Math.ceil(transaction.amount) - transaction.amount;
        return {
          ...transaction,
          round_up: roundUp,
          processed: true,
          account_name: account.account_name
        };
      };

      const transaction = { amount: 5.25, vendor: 'Test Store' };

      // Active account should process
      const activeResult = processTransaction(transaction, mockActiveAccount);
      expect(activeResult).not.toBeNull();
      expect(activeResult.round_up).toBe(0.75);
      expect(activeResult.processed).toBe(true);

      // Paused account should not process
      const pausedResult = processTransaction(transaction, mockPausedAccount);
      expect(pausedResult).toBeNull();

      // Deleted account should not process
      const deletedResult = processTransaction(transaction, mockDeletedAccount);
      expect(deletedResult).toBeNull();
    });
  });

  describe('Integration Scenarios', () => {
    test('should handle mixed account states in batch processing', () => {
      const accounts = [mockActiveAccount, mockPausedAccount, mockDeletedAccount];
      const transactions = [
        { account_id: 'active-account', amount: 5.25 },
        { account_id: 'paused-account', amount: 10.75 },
        { account_id: 'deleted-account', amount: 15.50 }
      ];

      const accountMap = new Map(accounts.map(acc => [acc.id, acc]));
      
      const results = transactions.map(transaction => {
        const account = accountMap.get(transaction.account_id);
        if (!account || !shouldProcessRoundUps(account)) {
          return {
            ...transaction,
            skipped: true,
            reason: !account ? 'Account not found' : 'Account not eligible for round-ups'
          };
        }

        return {
          ...transaction,
          round_up: Math.ceil(transaction.amount) - transaction.amount,
          processed: true
        };
      });

      const processed = results.filter(r => r.processed);
      const skipped = results.filter(r => r.skipped);

      expect(processed).toHaveLength(1);
      expect(skipped).toHaveLength(2);
      expect(processed[0].account_id).toBe('active-account');
    });

    test('should maintain transaction history for all accounts', () => {
      // Even paused accounts should maintain transaction history
      // Only the round-up processing should be skipped
      const transactions = [
        { account_id: 'active-account', amount: 5.25 },
        { account_id: 'paused-account', amount: 10.75 }
      ];

      const processedTransactions = transactions.map(transaction => ({
        ...transaction,
        stored: true, // All transactions are stored
        round_up_processed: transaction.account_id === 'active-account' // Only active accounts process round-ups
      }));

      expect(processedTransactions).toHaveLength(2);
      expect(processedTransactions.every(t => t.stored)).toBe(true);
      expect(processedTransactions.filter(t => t.round_up_processed)).toHaveLength(1);
    });
  });
});

// Export for use in other test files
module.exports = {
  mockActiveAccount,
  mockPausedAccount,
  mockDeletedAccount,
  mockInactiveAccount,
  shouldProcessRoundUps
};
