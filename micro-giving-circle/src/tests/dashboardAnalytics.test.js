/**
 * Unit Tests for Dashboard Analytics
 * Tests the account status analytics and insights
 */

// Mock dashboard data with account summary
const mockDashboardData = {
  user_id: 'test-user',
  full_name: 'Test User',
  monthly_goal: 50,
  total_donated_this_month: 25.75,
  total_round_ups_this_month: 15.50,
  charities_supported: 3,
  account_summary: {
    total: 4,
    active: 2,
    paused: 1,
    inactive: 0,
    deleted: 1
  }
};

const mockDashboardDataAllActive = {
  ...mockDashboardData,
  account_summary: {
    total: 3,
    active: 3,
    paused: 0,
    inactive: 0,
    deleted: 0
  }
};

const mockDashboardDataNone = {
  ...mockDashboardData,
  account_summary: {
    total: 0,
    active: 0,
    paused: 0,
    inactive: 0,
    deleted: 0
  }
};

const mockDashboardDataMostlyPaused = {
  ...mockDashboardData,
  account_summary: {
    total: 5,
    active: 1,
    paused: 3,
    inactive: 1,
    deleted: 0
  }
};

describe('Dashboard Analytics', () => {
  describe('Account Status Summary', () => {
    test('should calculate account totals correctly', () => {
      const { account_summary } = mockDashboardData;
      
      expect(account_summary.total).toBe(4);
      expect(account_summary.active).toBe(2);
      expect(account_summary.paused).toBe(1);
      expect(account_summary.inactive).toBe(0);
      expect(account_summary.deleted).toBe(1);
    });

    test('should calculate round-up efficiency correctly', () => {
      const calculateEfficiency = (active, total) => {
        return total > 0 ? (active / total) * 100 : 0;
      };

      const efficiency1 = calculateEfficiency(2, 4);
      expect(efficiency1).toBe(50);

      const efficiency2 = calculateEfficiency(3, 3);
      expect(efficiency2).toBe(100);

      const efficiency3 = calculateEfficiency(0, 0);
      expect(efficiency3).toBe(0);

      const efficiency4 = calculateEfficiency(1, 5);
      expect(efficiency4).toBe(20);
    });

    test('should handle edge cases', () => {
      const calculateEfficiency = (active, total) => {
        return total > 0 ? (active / total) * 100 : 0;
      };

      // No accounts
      expect(calculateEfficiency(0, 0)).toBe(0);
      
      // All accounts deleted/inactive
      expect(calculateEfficiency(0, 3)).toBe(0);
      
      // More active than total (shouldn't happen but handle gracefully)
      expect(calculateEfficiency(5, 3)).toBeGreaterThan(100);
    });
  });

  describe('Dashboard Insights Logic', () => {
    test('should identify when user has paused accounts', () => {
      const hasPausedAccounts = (data) => data.account_summary.paused > 0;
      
      expect(hasPausedAccounts(mockDashboardData)).toBe(true);
      expect(hasPausedAccounts(mockDashboardDataAllActive)).toBe(false);
      expect(hasPausedAccounts(mockDashboardDataNone)).toBe(false);
    });

    test('should identify when user has no accounts', () => {
      const hasNoAccounts = (data) => data.account_summary.total === 0;
      
      expect(hasNoAccounts(mockDashboardData)).toBe(false);
      expect(hasNoAccounts(mockDashboardDataAllActive)).toBe(false);
      expect(hasNoAccounts(mockDashboardDataNone)).toBe(true);
    });

    test('should identify when all accounts are active', () => {
      const allAccountsActive = (data) => {
        return data.account_summary.active > 0 && 
               data.account_summary.paused === 0 &&
               data.account_summary.active === data.account_summary.total - data.account_summary.deleted;
      };
      
      expect(allAccountsActive(mockDashboardData)).toBe(false);
      expect(allAccountsActive(mockDashboardDataAllActive)).toBe(true);
      expect(allAccountsActive(mockDashboardDataNone)).toBe(false);
    });

    test('should identify low efficiency accounts', () => {
      const hasLowEfficiency = (data) => {
        if (data.account_summary.total === 0) return false;
        return (data.account_summary.active / data.account_summary.total) < 0.5;
      };
      
      expect(hasLowEfficiency(mockDashboardData)).toBe(false); // 50% efficiency
      expect(hasLowEfficiency(mockDashboardDataAllActive)).toBe(false); // 100% efficiency
      expect(hasLowEfficiency(mockDashboardDataNone)).toBe(false); // No accounts
      expect(hasLowEfficiency(mockDashboardDataMostlyPaused)).toBe(true); // 20% efficiency
    });
  });

  describe('Insight Message Generation', () => {
    test('should generate appropriate insight messages', () => {
      const generateInsights = (data) => {
        const insights = [];
        
        if (data.account_summary.total === 0) {
          insights.push({
            type: 'info',
            title: 'Connect your first account',
            message: 'Link your debit card, credit card, or bank account to start generating round-ups for charity.'
          });
        }
        
        if (data.account_summary.paused > 0) {
          insights.push({
            type: 'warning',
            title: `You have ${data.account_summary.paused} paused account${data.account_summary.paused > 1 ? 's' : ''}`,
            message: 'Resume round-ups to increase your giving impact. Visit the Accounts page to manage your settings.'
          });
        }
        
        if (data.account_summary.active > 0 && data.account_summary.paused === 0) {
          insights.push({
            type: 'success',
            title: 'All accounts are active!',
            message: `Great job! All ${data.account_summary.active} of your accounts are generating round-ups for charity.`
          });
        }
        
        if (data.account_summary.total > 0 && (data.account_summary.active / data.account_summary.total) < 0.5) {
          const efficiency = Math.round((data.account_summary.active / data.account_summary.total) * 100);
          insights.push({
            type: 'boost',
            title: 'Boost your impact',
            message: `Only ${efficiency}% of your accounts are active. Consider resuming paused accounts to increase your giving potential.`
          });
        }
        
        return insights;
      };

      // Test no accounts
      const noAccountsInsights = generateInsights(mockDashboardDataNone);
      expect(noAccountsInsights).toHaveLength(1);
      expect(noAccountsInsights[0].type).toBe('info');

      // Test all active
      const allActiveInsights = generateInsights(mockDashboardDataAllActive);
      expect(allActiveInsights).toHaveLength(1);
      expect(allActiveInsights[0].type).toBe('success');

      // Test mixed accounts
      const mixedInsights = generateInsights(mockDashboardData);
      expect(mixedInsights.length).toBeGreaterThan(0);
      expect(mixedInsights.some(insight => insight.type === 'warning')).toBe(true);

      // Test low efficiency
      const lowEfficiencyInsights = generateInsights(mockDashboardDataMostlyPaused);
      expect(lowEfficiencyInsights.some(insight => insight.type === 'boost')).toBe(true);
      expect(lowEfficiencyInsights.some(insight => insight.type === 'warning')).toBe(true);
    });
  });

  describe('Dashboard Metrics Calculation', () => {
    test('should calculate progress towards monthly goal', () => {
      const calculateProgress = (donated, goal) => {
        return goal > 0 ? (donated / goal) * 100 : 0;
      };

      expect(calculateProgress(25.75, 50)).toBe(51.5);
      expect(calculateProgress(0, 50)).toBe(0);
      expect(calculateProgress(75, 50)).toBe(150);
      expect(calculateProgress(25, 0)).toBe(0);
    });

    test('should format currency values correctly', () => {
      const formatCurrency = (amount) => `$${amount.toFixed(2)}`;

      expect(formatCurrency(25.75)).toBe('$25.75');
      expect(formatCurrency(0)).toBe('$0.00');
      expect(formatCurrency(100.5)).toBe('$100.50');
      expect(formatCurrency(1000)).toBe('$1000.00');
    });

    test('should calculate account distribution percentages', () => {
      const calculateDistribution = (summary) => {
        if (summary.total === 0) return { active: 0, paused: 0, inactive: 0, deleted: 0 };
        
        return {
          active: Math.round((summary.active / summary.total) * 100),
          paused: Math.round((summary.paused / summary.total) * 100),
          inactive: Math.round((summary.inactive / summary.total) * 100),
          deleted: Math.round((summary.deleted / summary.total) * 100)
        };
      };

      const distribution = calculateDistribution(mockDashboardData.account_summary);
      expect(distribution.active).toBe(50); // 2/4 = 50%
      expect(distribution.paused).toBe(25); // 1/4 = 25%
      expect(distribution.inactive).toBe(0); // 0/4 = 0%
      expect(distribution.deleted).toBe(25); // 1/4 = 25%

      const noAccountsDistribution = calculateDistribution(mockDashboardDataNone.account_summary);
      expect(noAccountsDistribution.active).toBe(0);
      expect(noAccountsDistribution.paused).toBe(0);
      expect(noAccountsDistribution.inactive).toBe(0);
      expect(noAccountsDistribution.deleted).toBe(0);
    });
  });

  describe('Real-time Updates', () => {
    test('should handle account status changes', () => {
      const updateAccountStatus = (currentSummary, accountId, newStatus) => {
        // Simulate updating an account status
        const newSummary = { ...currentSummary };
        
        // This is a simplified version - in reality you'd track individual accounts
        if (newStatus === 'paused') {
          newSummary.active = Math.max(0, newSummary.active - 1);
          newSummary.paused = newSummary.paused + 1;
        } else if (newStatus === 'active') {
          newSummary.paused = Math.max(0, newSummary.paused - 1);
          newSummary.active = newSummary.active + 1;
        }
        
        return newSummary;
      };

      const originalSummary = { ...mockDashboardData.account_summary };
      
      // Pause an active account
      const afterPause = updateAccountStatus(originalSummary, 'account-1', 'paused');
      expect(afterPause.active).toBe(1);
      expect(afterPause.paused).toBe(2);

      // Resume a paused account
      const afterResume = updateAccountStatus(afterPause, 'account-2', 'active');
      expect(afterResume.active).toBe(2);
      expect(afterResume.paused).toBe(1);
    });
  });
});

// Export for use in other test files
module.exports = {
  mockDashboardData,
  mockDashboardDataAllActive,
  mockDashboardDataNone,
  mockDashboardDataMostlyPaused
};
