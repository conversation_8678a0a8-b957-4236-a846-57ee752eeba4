/**
 * Unit Tests for Security and Audit System
 * Tests audit logging, rate limiting, and suspicious activity detection
 */

// Mock security data with recent timestamps
const now = new Date();
const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
const twoHoursAgo = new Date(now.getTime() - 2 * 60 * 60 * 1000);

const mockAuditLogs = [
  {
    id: 'audit-1',
    user_id: 'user-123',
    account_id: 'account-1',
    action: 'pause',
    old_values: { rounding_enabled: true },
    new_values: { rounding_enabled: false },
    changed_fields: ['rounding_enabled'],
    reason: 'Vacation',
    ip_address: '*************',
    user_agent: 'Mozilla/5.0...',
    created_at: oneHourAgo.toISOString()
  },
  {
    id: 'audit-2',
    user_id: 'user-123',
    account_id: 'account-2',
    action: 'bulk_pause',
    old_values: { rounding_enabled: true },
    new_values: { rounding_enabled: false },
    changed_fields: ['rounding_enabled'],
    reason: 'Budget constraints',
    ip_address: '*************',
    user_agent: 'Mozilla/5.0...',
    created_at: twoHoursAgo.toISOString()
  }
];

const mockSecurityAlerts = [
  {
    id: 'alert-1',
    user_id: 'user-123',
    alert_type: 'rate_limit_exceeded',
    severity: 'medium',
    title: 'Rate Limit Exceeded',
    description: 'User exceeded rate limit for pause operations',
    source_ip: '*************',
    resolved: false,
    created_at: oneHourAgo.toISOString()
  },
  {
    id: 'alert-2',
    user_id: 'user-456',
    alert_type: 'suspicious_activity',
    severity: 'high',
    title: 'Suspicious Activity Detected',
    description: 'High-risk activity detected for user account operations',
    source_ip: '*********',
    resolved: false,
    created_at: twoHoursAgo.toISOString()
  }
];

const mockSuspiciousActivity = [
  {
    id: 'suspicious-1',
    user_id: 'user-456',
    activity_type: 'bulk_pause',
    risk_score: 85,
    indicators: {
      rapid_actions: 8,
      multiple_ips: 4,
      off_hours_bulk: true
    },
    source_ip: '*********',
    investigated: false,
    created_at: oneHourAgo.toISOString()
  }
];

describe('Security and Audit System', () => {
  describe('Audit Logging', () => {
    test('should log account changes with complete audit trail', () => {
      const logAuditEntry = (userId, accountId, action, oldValues, newValues, reason) => {
        const changedFields = Object.keys(newValues).filter(
          key => oldValues[key] !== newValues[key]
        );

        return {
          id: `audit-${Date.now()}`,
          user_id: userId,
          account_id: accountId,
          action,
          old_values: oldValues,
          new_values: newValues,
          changed_fields: changedFields,
          reason,
          ip_address: '*************',
          user_agent: 'Mozilla/5.0...',
          session_id: 'session-123',
          request_id: 'req-456',
          created_at: new Date().toISOString()
        };
      };

      const oldState = { rounding_enabled: true, is_active: true };
      const newState = { rounding_enabled: false, is_active: true };
      
      const auditEntry = logAuditEntry(
        'user-123',
        'account-1',
        'pause',
        oldState,
        newState,
        'Vacation'
      );

      expect(auditEntry.user_id).toBe('user-123');
      expect(auditEntry.account_id).toBe('account-1');
      expect(auditEntry.action).toBe('pause');
      expect(auditEntry.changed_fields).toEqual(['rounding_enabled']);
      expect(auditEntry.reason).toBe('Vacation');
      expect(auditEntry.old_values.rounding_enabled).toBe(true);
      expect(auditEntry.new_values.rounding_enabled).toBe(false);
    });

    test('should track security context in audit logs', () => {
      const logSecurityContext = (userId, action, securityContext) => {
        return {
          id: `audit-${Date.now()}`,
          user_id: userId,
          action,
          security_context: securityContext,
          created_at: new Date().toISOString()
        };
      };

      const securityContext = {
        execution_time_ms: 250,
        success: true,
        timestamp: '2024-01-15T10:30:00Z',
        api_endpoint: '/api/accounts/pause',
        http_method: 'POST'
      };

      const auditEntry = logSecurityContext('user-123', 'pause', securityContext);
      
      expect(auditEntry.security_context.execution_time_ms).toBe(250);
      expect(auditEntry.security_context.success).toBe(true);
      expect(auditEntry.security_context.api_endpoint).toBe('/api/accounts/pause');
    });

    test('should handle bulk operations in audit logs', () => {
      const logBulkOperation = (userId, action, accountIds, reason) => {
        return accountIds.map(accountId => ({
          id: `audit-${Date.now()}-${accountId}`,
          user_id: userId,
          account_id: accountId,
          action,
          reason,
          operation_subtype: 'bulk',
          created_at: new Date().toISOString()
        }));
      };

      const accountIds = ['account-1', 'account-2', 'account-3'];
      const auditEntries = logBulkOperation('user-123', 'bulk_pause', accountIds, 'Budget constraints');

      expect(auditEntries).toHaveLength(3);
      expect(auditEntries[0].operation_subtype).toBe('bulk');
      expect(auditEntries[0].reason).toBe('Budget constraints');
      expect(auditEntries.every(entry => entry.action === 'bulk_pause')).toBe(true);
    });
  });

  describe('Rate Limiting', () => {
    test('should enforce rate limits correctly', () => {
      const rateLimits = new Map();

      const checkRateLimit = (userId, operationType, limit = 10, windowMinutes = 60) => {
        const now = Date.now();
        const windowStart = now - (windowMinutes * 60 * 1000);
        const key = `${userId}:${operationType}`;
        
        // Get or create rate limit record
        if (!rateLimits.has(key)) {
          rateLimits.set(key, []);
        }
        
        const requests = rateLimits.get(key);
        
        // Remove old requests outside the window
        const validRequests = requests.filter(timestamp => timestamp > windowStart);
        
        // Check if limit exceeded
        if (validRequests.length >= limit) {
          return { allowed: false, remaining: 0 };
        }
        
        // Add current request
        validRequests.push(now);
        rateLimits.set(key, validRequests);
        
        return { 
          allowed: true, 
          remaining: limit - validRequests.length 
        };
      };

      // Test normal usage
      let result = checkRateLimit('user-123', 'pause', 5, 60);
      expect(result.allowed).toBe(true);
      expect(result.remaining).toBe(4);

      // Test approaching limit
      for (let i = 0; i < 4; i++) {
        result = checkRateLimit('user-123', 'pause', 5, 60);
      }
      expect(result.allowed).toBe(true);
      expect(result.remaining).toBe(0);

      // Test exceeding limit
      result = checkRateLimit('user-123', 'pause', 5, 60);
      expect(result.allowed).toBe(false);
      expect(result.remaining).toBe(0);
    });

    test('should handle different operation types separately', () => {
      const rateLimits = new Map();

      const checkRateLimit = (userId, operationType, limit = 10) => {
        const key = `${userId}:${operationType}`;
        const count = rateLimits.get(key) || 0;
        
        if (count >= limit) {
          return { allowed: false };
        }
        
        rateLimits.set(key, count + 1);
        return { allowed: true };
      };

      // Different operation types should have separate limits
      expect(checkRateLimit('user-123', 'pause', 2).allowed).toBe(true);
      expect(checkRateLimit('user-123', 'resume', 2).allowed).toBe(true);
      expect(checkRateLimit('user-123', 'pause', 2).allowed).toBe(true);
      expect(checkRateLimit('user-123', 'resume', 2).allowed).toBe(true);
      
      // Should exceed limit for pause but not resume
      expect(checkRateLimit('user-123', 'pause', 2).allowed).toBe(false);
      expect(checkRateLimit('user-123', 'resume', 2).allowed).toBe(false);
    });

    test('should reset rate limits after time window', () => {
      const rateLimits = new Map();

      const checkRateLimitWithTime = (userId, operationType, limit, windowMinutes, currentTime) => {
        const windowStart = currentTime - (windowMinutes * 60 * 1000);
        const key = `${userId}:${operationType}`;
        
        if (!rateLimits.has(key)) {
          rateLimits.set(key, []);
        }
        
        const requests = rateLimits.get(key);
        const validRequests = requests.filter(timestamp => timestamp > windowStart);
        
        if (validRequests.length >= limit) {
          return { allowed: false };
        }
        
        validRequests.push(currentTime);
        rateLimits.set(key, validRequests);
        return { allowed: true };
      };

      const baseTime = Date.now();
      
      // Fill up the rate limit
      for (let i = 0; i < 5; i++) {
        expect(checkRateLimitWithTime('user-123', 'pause', 5, 60, baseTime + i * 1000).allowed).toBe(true);
      }
      
      // Should be blocked
      expect(checkRateLimitWithTime('user-123', 'pause', 5, 60, baseTime + 5000).allowed).toBe(false);
      
      // After window expires, should be allowed again
      const afterWindow = baseTime + (61 * 60 * 1000); // 61 minutes later
      expect(checkRateLimitWithTime('user-123', 'pause', 5, 60, afterWindow).allowed).toBe(true);
    });
  });

  describe('Suspicious Activity Detection', () => {
    test('should detect rapid successive actions', () => {
      const analyzeSuspiciousActivity = (userActions) => {
        let riskScore = 0;
        const indicators = {};
        
        // Check for rapid actions (more than 5 in 1 minute)
        const oneMinuteAgo = Date.now() - (60 * 1000);
        const recentActions = userActions.filter(action => action.timestamp > oneMinuteAgo);
        
        if (recentActions.length > 5) {
          riskScore += 30;
          indicators.rapid_actions = recentActions.length;
        }
        
        // Check for high activity (more than 20 in 1 hour)
        const oneHourAgo = Date.now() - (60 * 60 * 1000);
        const hourlyActions = userActions.filter(action => action.timestamp > oneHourAgo);
        
        if (hourlyActions.length > 20) {
          riskScore += 25;
          indicators.high_activity = hourlyActions.length;
        }
        
        return { riskScore, indicators };
      };

      const now = Date.now();
      const rapidActions = Array.from({ length: 8 }, (_, i) => ({
        timestamp: now - (i * 5000), // 8 actions in 40 seconds
        action: 'pause'
      }));

      const result = analyzeSuspiciousActivity(rapidActions);
      expect(result.riskScore).toBe(30);
      expect(result.indicators.rapid_actions).toBe(8);
    });

    test('should detect multiple IP addresses', () => {
      const analyzeIPPatterns = (userActions) => {
        let riskScore = 0;
        const indicators = {};
        
        const oneHourAgo = Date.now() - (60 * 60 * 1000);
        const recentActions = userActions.filter(action => action.timestamp > oneHourAgo);
        const uniqueIPs = new Set(recentActions.map(action => action.ip_address));
        
        if (uniqueIPs.size > 3) {
          riskScore += 40;
          indicators.multiple_ips = uniqueIPs.size;
        }
        
        return { riskScore, indicators };
      };

      const now = Date.now();
      const multiIPActions = [
        { timestamp: now - 1000, ip_address: '*************' },
        { timestamp: now - 2000, ip_address: '*********' },
        { timestamp: now - 3000, ip_address: '***********' },
        { timestamp: now - 4000, ip_address: '***********' },
        { timestamp: now - 5000, ip_address: '*************' }
      ];

      const result = analyzeIPPatterns(multiIPActions);
      expect(result.riskScore).toBe(40);
      expect(result.indicators.multiple_ips).toBe(5);
    });

    test('should detect off-hours bulk operations', () => {
      const analyzeOffHoursActivity = (action, timestamp) => {
        let riskScore = 0;
        const indicators = {};
        
        const hour = new Date(timestamp).getUTCHours();
        const isBulkOperation = action.startsWith('bulk_');
        const isOffHours = hour < 6 || hour > 23;
        
        if (isBulkOperation && isOffHours) {
          riskScore += 20;
          indicators.off_hours_bulk = true;
        }
        
        return { riskScore, indicators };
      };

      // Test bulk operation at 3 AM UTC
      const offHoursTime = new Date('2024-01-15T03:00:00Z').getTime();
      const result = analyzeOffHoursActivity('bulk_pause', offHoursTime);
      
      expect(result.riskScore).toBe(20);
      expect(result.indicators.off_hours_bulk).toBe(true);

      // Test normal operation during business hours
      const businessHoursTime = new Date('2024-01-15T14:00:00Z').getTime();
      const normalResult = analyzeOffHoursActivity('bulk_pause', businessHoursTime);
      
      expect(normalResult.riskScore).toBe(0);
      expect(normalResult.indicators.off_hours_bulk).toBeUndefined();
    });

    test('should calculate composite risk scores', () => {
      const calculateCompositeRisk = (indicators) => {
        let totalRisk = 0;
        
        if (indicators.rapid_actions > 5) totalRisk += 30;
        if (indicators.high_activity > 20) totalRisk += 25;
        if (indicators.multiple_ips > 3) totalRisk += 40;
        if (indicators.off_hours_bulk) totalRisk += 20;
        
        return Math.min(totalRisk, 100); // Cap at 100
      };

      const highRiskIndicators = {
        rapid_actions: 8,
        high_activity: 25,
        multiple_ips: 5,
        off_hours_bulk: true
      };

      const riskScore = calculateCompositeRisk(highRiskIndicators);
      expect(riskScore).toBe(100); // Should be capped at 100

      const mediumRiskIndicators = {
        rapid_actions: 6,
        multiple_ips: 4
      };

      const mediumRisk = calculateCompositeRisk(mediumRiskIndicators);
      expect(mediumRisk).toBe(70); // 30 + 40
    });
  });

  describe('Security Dashboard Data', () => {
    test('should aggregate security metrics correctly', () => {
      const aggregateSecurityData = (auditLogs, alerts, suspiciousActivities, timeWindowHours = 24) => {
        const windowStart = Date.now() - (timeWindowHours * 60 * 60 * 1000);
        
        const recentAudits = auditLogs.filter(log => 
          new Date(log.created_at).getTime() > windowStart
        );
        
        const recentAlerts = alerts.filter(alert => 
          new Date(alert.created_at).getTime() > windowStart && !alert.resolved
        );
        
        const recentSuspicious = suspiciousActivities.filter(activity => 
          new Date(activity.created_at).getTime() > windowStart
        );

        const uniqueUsers = new Set(recentAudits.map(log => log.user_id)).size;
        
        const topActions = recentAudits.reduce((acc, log) => {
          acc[log.action] = (acc[log.action] || 0) + 1;
          return acc;
        }, {});

        const riskDistribution = recentSuspicious.reduce((acc, activity) => {
          const level = activity.risk_score >= 75 ? 'high' : 
                       activity.risk_score >= 50 ? 'medium' : 'low';
          acc[level] = (acc[level] || 0) + 1;
          return acc;
        }, {});

        return {
          total_audit_entries: recentAudits.length,
          unique_users: uniqueUsers,
          security_alerts: recentAlerts.length,
          suspicious_activities: recentSuspicious.length,
          rate_limit_violations: recentAlerts.filter(a => a.alert_type === 'rate_limit_exceeded').length,
          top_actions: topActions,
          risk_distribution: riskDistribution
        };
      };

      const dashboardData = aggregateSecurityData(
        mockAuditLogs,
        mockSecurityAlerts,
        mockSuspiciousActivity,
        24
      );

      expect(dashboardData.total_audit_entries).toBe(2);
      expect(dashboardData.unique_users).toBe(1);
      expect(dashboardData.security_alerts).toBe(2);
      expect(dashboardData.suspicious_activities).toBe(1);
      expect(dashboardData.rate_limit_violations).toBe(1);
      expect(dashboardData.top_actions.pause).toBe(1);
      expect(dashboardData.top_actions.bulk_pause).toBe(1);
      expect(dashboardData.risk_distribution.high).toBe(1);
    });

    test('should handle empty data gracefully', () => {
      const aggregateSecurityData = (auditLogs, alerts, suspiciousActivities) => {
        return {
          total_audit_entries: auditLogs.length,
          unique_users: new Set(auditLogs.map(log => log.user_id)).size,
          security_alerts: alerts.filter(a => !a.resolved).length,
          suspicious_activities: suspiciousActivities.length,
          rate_limit_violations: alerts.filter(a => a.alert_type === 'rate_limit_exceeded' && !a.resolved).length,
          top_actions: {},
          risk_distribution: {}
        };
      };

      const emptyData = aggregateSecurityData([], [], []);
      
      expect(emptyData.total_audit_entries).toBe(0);
      expect(emptyData.unique_users).toBe(0);
      expect(emptyData.security_alerts).toBe(0);
      expect(emptyData.suspicious_activities).toBe(0);
      expect(emptyData.rate_limit_violations).toBe(0);
      expect(emptyData.top_actions).toEqual({});
      expect(emptyData.risk_distribution).toEqual({});
    });
  });
});

// Export for use in other test files
module.exports = {
  mockAuditLogs,
  mockSecurityAlerts,
  mockSuspiciousActivity
};
