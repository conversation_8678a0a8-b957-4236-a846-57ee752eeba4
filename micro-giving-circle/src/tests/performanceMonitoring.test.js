/**
 * Unit Tests for Performance Monitoring System
 * Tests metrics collection, analysis, and alerting functionality
 */

// Mock performance data
const mockOperationMetrics = [
  {
    id: 'metric-1',
    user_id: 'user-123',
    operation_type: 'pause',
    operation_subtype: 'individual',
    account_count: 1,
    execution_time_ms: 250,
    success: true,
    created_at: '2024-01-15T10:30:00Z'
  },
  {
    id: 'metric-2',
    user_id: 'user-123',
    operation_type: 'bulk_pause',
    operation_subtype: 'bulk',
    account_count: 5,
    execution_time_ms: 1200,
    success: true,
    created_at: '2024-01-15T10:35:00Z'
  },
  {
    id: 'metric-3',
    user_id: 'user-123',
    operation_type: 'resume',
    operation_subtype: 'individual',
    account_count: 1,
    execution_time_ms: 8000,
    success: false,
    error_code: 'TIMEOUT_ERROR',
    error_message: 'Operation timed out',
    created_at: '2024-01-15T10:40:00Z'
  }
];

const mockPerformanceAlerts = [
  {
    id: 'alert-1',
    alert_type: 'high_error_rate',
    severity: 'high',
    title: 'High Error Rate Detected',
    description: 'Error rate for pause/resume operations is above threshold',
    threshold_value: 10,
    actual_value: 25,
    time_window_minutes: 60,
    affected_users: 5,
    resolved: false,
    created_at: '2024-01-15T10:45:00Z'
  },
  {
    id: 'alert-2',
    alert_type: 'slow_operations',
    severity: 'medium',
    title: 'Slow Operations Detected',
    description: 'Average execution time is above threshold',
    threshold_value: 5000,
    actual_value: 7500,
    time_window_minutes: 60,
    resolved: false,
    created_at: '2024-01-15T11:00:00Z'
  }
];

const mockBehaviorPatterns = [
  {
    id: 'pattern-1',
    user_id: 'user-123',
    pattern_type: 'frequent_pauser',
    pattern_score: 85.5,
    pattern_data: { pause_count: 15, period_days: 30 },
    first_detected: '2024-01-01T00:00:00Z',
    last_updated: '2024-01-15T00:00:00Z',
    active: true
  },
  {
    id: 'pattern-2',
    user_id: 'user-123',
    pattern_type: 'bulk_operator',
    pattern_score: 60.0,
    pattern_data: { bulk_count: 4, period_days: 30 },
    first_detected: '2024-01-05T00:00:00Z',
    last_updated: '2024-01-15T00:00:00Z',
    active: true
  }
];

describe('Performance Monitoring System', () => {
  describe('Metrics Collection', () => {
    test('should collect operation metrics correctly', () => {
      const collectMetric = (operationType, executionTime, success, accountCount = 1) => {
        return {
          id: `metric-${Date.now()}`,
          operation_type: operationType,
          execution_time_ms: executionTime,
          success,
          account_count: accountCount,
          created_at: new Date().toISOString()
        };
      };

      const pauseMetric = collectMetric('pause', 300, true);
      expect(pauseMetric.operation_type).toBe('pause');
      expect(pauseMetric.execution_time_ms).toBe(300);
      expect(pauseMetric.success).toBe(true);

      const bulkMetric = collectMetric('bulk_pause', 1500, true, 10);
      expect(bulkMetric.account_count).toBe(10);
    });

    test('should handle error metrics', () => {
      const collectErrorMetric = (operationType, error) => {
        return {
          id: `metric-${Date.now()}`,
          operation_type: operationType,
          success: false,
          error_code: error.code,
          error_message: error.message,
          created_at: new Date().toISOString()
        };
      };

      const errorMetric = collectErrorMetric('pause', { 
        code: 'NETWORK_ERROR', 
        message: 'Failed to connect to database' 
      });
      
      expect(errorMetric.success).toBe(false);
      expect(errorMetric.error_code).toBe('NETWORK_ERROR');
      expect(errorMetric.error_message).toBe('Failed to connect to database');
    });

    test('should track execution time accurately', () => {
      const measureExecutionTime = async (operation) => {
        const startTime = Date.now();
        await operation();
        return Date.now() - startTime;
      };

      const mockOperation = () => new Promise(resolve => setTimeout(resolve, 100));
      
      return measureExecutionTime(mockOperation).then(executionTime => {
        expect(executionTime).toBeGreaterThanOrEqual(100);
        expect(executionTime).toBeLessThan(200); // Allow some variance
      });
    });
  });

  describe('Performance Analysis', () => {
    test('should calculate error rates correctly', () => {
      const calculateErrorRate = (metrics) => {
        const totalOperations = metrics.length;
        const failedOperations = metrics.filter(m => !m.success).length;
        return totalOperations > 0 ? (failedOperations / totalOperations) * 100 : 0;
      };

      const errorRate = calculateErrorRate(mockOperationMetrics);
      expect(errorRate).toBeCloseTo(33.33, 2); // 1 failed out of 3 total
    });

    test('should calculate average execution time', () => {
      const calculateAvgExecutionTime = (metrics) => {
        const successfulMetrics = metrics.filter(m => m.success && m.execution_time_ms);
        if (successfulMetrics.length === 0) return 0;
        
        const totalTime = successfulMetrics.reduce((sum, m) => sum + m.execution_time_ms, 0);
        return totalTime / successfulMetrics.length;
      };

      const avgTime = calculateAvgExecutionTime(mockOperationMetrics);
      expect(avgTime).toBe(725); // (250 + 1200) / 2
    });

    test('should group operations by type', () => {
      const groupByOperationType = (metrics) => {
        return metrics.reduce((groups, metric) => {
          const type = metric.operation_type;
          groups[type] = (groups[type] || 0) + 1;
          return groups;
        }, {});
      };

      const grouped = groupByOperationType(mockOperationMetrics);
      expect(grouped.pause).toBe(1);
      expect(grouped.bulk_pause).toBe(1);
      expect(grouped.resume).toBe(1);
    });

    test('should identify top errors', () => {
      const getTopErrors = (metrics, limit = 5) => {
        const errorCounts = {};
        const totalErrors = metrics.filter(m => !m.success).length;
        
        metrics.filter(m => !m.success).forEach(metric => {
          const errorCode = metric.error_code || 'unknown';
          errorCounts[errorCode] = (errorCounts[errorCode] || 0) + 1;
        });

        return Object.entries(errorCounts)
          .map(([error_code, count]) => ({
            error_code,
            count,
            percentage: totalErrors > 0 ? (count / totalErrors) * 100 : 0
          }))
          .sort((a, b) => b.count - a.count)
          .slice(0, limit);
      };

      const topErrors = getTopErrors(mockOperationMetrics);
      expect(topErrors).toHaveLength(1);
      expect(topErrors[0].error_code).toBe('TIMEOUT_ERROR');
      expect(topErrors[0].count).toBe(1);
      expect(topErrors[0].percentage).toBe(100);
    });
  });

  describe('Alert System', () => {
    test('should detect high error rates', () => {
      const checkErrorRateAlert = (metrics, threshold = 10) => {
        const errorRate = (metrics.filter(m => !m.success).length / metrics.length) * 100;
        
        if (errorRate > threshold) {
          return {
            alert_type: 'high_error_rate',
            severity: errorRate > 25 ? 'critical' : errorRate > 20 ? 'high' : 'medium',
            title: 'High Error Rate Detected',
            threshold_value: threshold,
            actual_value: errorRate
          };
        }
        
        return null;
      };

      const alert = checkErrorRateAlert(mockOperationMetrics, 10);
      expect(alert).not.toBeNull();
      expect(alert.alert_type).toBe('high_error_rate');
      expect(alert.severity).toBe('critical'); // 33.33% > 25%
    });

    test('should detect slow operations', () => {
      const checkSlowOperationsAlert = (metrics, threshold = 5000) => {
        const successfulMetrics = metrics.filter(m => m.success && m.execution_time_ms);
        if (successfulMetrics.length === 0) return null;
        
        const avgTime = successfulMetrics.reduce((sum, m) => sum + m.execution_time_ms, 0) / successfulMetrics.length;
        
        if (avgTime > threshold) {
          return {
            alert_type: 'slow_operations',
            severity: avgTime > 10000 ? 'high' : 'medium',
            title: 'Slow Operations Detected',
            threshold_value: threshold,
            actual_value: avgTime
          };
        }
        
        return null;
      };

      const alert = checkSlowOperationsAlert(mockOperationMetrics, 1000);
      expect(alert).toBeNull(); // 725ms average is below 1000ms threshold

      const slowAlert = checkSlowOperationsAlert(mockOperationMetrics, 500);
      expect(slowAlert).not.toBeNull();
      expect(slowAlert.alert_type).toBe('slow_operations');
    });

    test('should prioritize alerts by severity', () => {
      const prioritizeAlerts = (alerts) => {
        const severityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
        return [...alerts].sort((a, b) => severityOrder[b.severity] - severityOrder[a.severity]);
      };

      const prioritized = prioritizeAlerts(mockPerformanceAlerts);
      expect(prioritized[0].severity).toBe('high');
      expect(prioritized[1].severity).toBe('medium');
    });
  });

  describe('Behavior Pattern Analysis', () => {
    test('should detect frequent pauser pattern', () => {
      const analyzeFrequentPauser = (userMetrics, days = 30) => {
        const pauseOperations = userMetrics.filter(m => 
          m.operation_type === 'pause' || m.operation_type === 'resume'
        );
        
        if (pauseOperations.length >= 10) {
          return {
            pattern_type: 'frequent_pauser',
            pattern_score: Math.min(pauseOperations.length * 5, 100),
            pattern_data: { pause_count: pauseOperations.length, period_days: days }
          };
        }
        
        return null;
      };

      // Create mock data with many pause operations
      const frequentPauserMetrics = Array.from({ length: 15 }, (_, i) => ({
        operation_type: i % 2 === 0 ? 'pause' : 'resume',
        user_id: 'user-123'
      }));

      const pattern = analyzeFrequentPauser(frequentPauserMetrics);
      expect(pattern).not.toBeNull();
      expect(pattern.pattern_type).toBe('frequent_pauser');
      expect(pattern.pattern_score).toBe(75); // 15 * 5 = 75
    });

    test('should detect bulk operator pattern', () => {
      const analyzeBulkOperator = (userMetrics, days = 30) => {
        const bulkOperations = userMetrics.filter(m => 
          m.operation_subtype === 'bulk'
        );
        
        if (bulkOperations.length >= 3) {
          return {
            pattern_type: 'bulk_operator',
            pattern_score: Math.min(bulkOperations.length * 15, 100),
            pattern_data: { bulk_count: bulkOperations.length, period_days: days }
          };
        }
        
        return null;
      };

      const bulkOperatorMetrics = Array.from({ length: 4 }, () => ({
        operation_subtype: 'bulk',
        user_id: 'user-123'
      }));

      const pattern = analyzeBulkOperator(bulkOperatorMetrics);
      expect(pattern).not.toBeNull();
      expect(pattern.pattern_type).toBe('bulk_operator');
      expect(pattern.pattern_score).toBe(60); // 4 * 15 = 60
    });

    test('should detect scheduler pattern', () => {
      const analyzeScheduler = (userMetrics, days = 30) => {
        const scheduleOperations = userMetrics.filter(m => 
          m.operation_type === 'schedule' || m.operation_type === 'cancel_schedule'
        );
        
        if (scheduleOperations.length >= 2) {
          return {
            pattern_type: 'scheduler',
            pattern_score: Math.min(scheduleOperations.length * 25, 100),
            pattern_data: { schedule_count: scheduleOperations.length, period_days: days }
          };
        }
        
        return null;
      };

      const schedulerMetrics = [
        { operation_type: 'schedule', user_id: 'user-123' },
        { operation_type: 'cancel_schedule', user_id: 'user-123' },
        { operation_type: 'schedule', user_id: 'user-123' }
      ];

      const pattern = analyzeScheduler(schedulerMetrics);
      expect(pattern).not.toBeNull();
      expect(pattern.pattern_type).toBe('scheduler');
      expect(pattern.pattern_score).toBe(75); // 3 * 25 = 75
    });
  });

  describe('Dashboard Data Aggregation', () => {
    test('should aggregate performance dashboard data', () => {
      const aggregateDashboardData = (metrics, timeWindowHours = 24) => {
        const totalOperations = metrics.length;
        const successfulOperations = metrics.filter(m => m.success).length;
        const failedOperations = totalOperations - successfulOperations;
        const errorRate = totalOperations > 0 ? (failedOperations / totalOperations) * 100 : 0;
        
        const successfulWithTime = metrics.filter(m => m.success && m.execution_time_ms);
        const avgExecutionTime = successfulWithTime.length > 0 
          ? successfulWithTime.reduce((sum, m) => sum + m.execution_time_ms, 0) / successfulWithTime.length
          : 0;

        const operationsByType = metrics.reduce((groups, metric) => {
          groups[metric.operation_type] = (groups[metric.operation_type] || 0) + 1;
          return groups;
        }, {});

        return {
          total_operations: totalOperations,
          successful_operations: successfulOperations,
          failed_operations: failedOperations,
          error_rate: Math.round(errorRate * 100) / 100,
          avg_execution_time: Math.round(avgExecutionTime * 100) / 100,
          operations_by_type: operationsByType
        };
      };

      const dashboardData = aggregateDashboardData(mockOperationMetrics);
      expect(dashboardData.total_operations).toBe(3);
      expect(dashboardData.successful_operations).toBe(2);
      expect(dashboardData.failed_operations).toBe(1);
      expect(dashboardData.error_rate).toBe(33.33);
      expect(dashboardData.avg_execution_time).toBe(725);
      expect(dashboardData.operations_by_type.pause).toBe(1);
    });

    test('should handle empty metrics gracefully', () => {
      const aggregateDashboardData = (metrics) => {
        const totalOperations = metrics.length;
        if (totalOperations === 0) {
          return {
            total_operations: 0,
            successful_operations: 0,
            failed_operations: 0,
            error_rate: 0,
            avg_execution_time: 0,
            operations_by_type: {}
          };
        }
      };

      const emptyData = aggregateDashboardData([]);
      expect(emptyData.total_operations).toBe(0);
      expect(emptyData.error_rate).toBe(0);
      expect(emptyData.operations_by_type).toEqual({});
    });
  });

  describe('Performance Optimization', () => {
    test('should identify performance bottlenecks', () => {
      const identifyBottlenecks = (metrics) => {
        const bottlenecks = [];
        
        // Check for slow operations
        const slowOperations = metrics.filter(m => m.execution_time_ms > 5000);
        if (slowOperations.length > 0) {
          bottlenecks.push({
            type: 'slow_operations',
            count: slowOperations.length,
            avg_time: slowOperations.reduce((sum, m) => sum + m.execution_time_ms, 0) / slowOperations.length
          });
        }
        
        // Check for high error rates by operation type
        const operationTypes = [...new Set(metrics.map(m => m.operation_type))];
        operationTypes.forEach(type => {
          const typeMetrics = metrics.filter(m => m.operation_type === type);
          const errorRate = (typeMetrics.filter(m => !m.success).length / typeMetrics.length) * 100;
          
          if (errorRate > 20) {
            bottlenecks.push({
              type: 'high_error_rate',
              operation_type: type,
              error_rate: errorRate,
              total_operations: typeMetrics.length
            });
          }
        });
        
        return bottlenecks;
      };

      const bottlenecks = identifyBottlenecks(mockOperationMetrics);
      expect(bottlenecks.length).toBeGreaterThan(0);
      expect(bottlenecks.some(b => b.type === 'high_error_rate')).toBe(true);
    });
  });
});

// Export for use in other test files
module.exports = {
  mockOperationMetrics,
  mockPerformanceAlerts,
  mockBehaviorPatterns
};
