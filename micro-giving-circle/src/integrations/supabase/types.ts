export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      charities: {
        Row: {
          category_id: number | null
          created_at: string | null
          description: string | null
          ein: string | null
          id: number
          impact_statement: string | null
          logo_url: string | null
          name: string
          rating: number | null
          total_donors: number | null
          total_raised: number | null
          updated_at: string | null
          verified: boolean | null
          website_url: string | null
        }
        Insert: {
          category_id?: number | null
          created_at?: string | null
          description?: string | null
          ein?: string | null
          id?: number
          impact_statement?: string | null
          logo_url?: string | null
          name: string
          rating?: number | null
          total_donors?: number | null
          total_raised?: number | null
          updated_at?: string | null
          verified?: boolean | null
          website_url?: string | null
        }
        Update: {
          category_id?: number | null
          created_at?: string | null
          description?: string | null
          ein?: string | null
          id?: number
          impact_statement?: string | null
          logo_url?: string | null
          name?: string
          rating?: number | null
          total_donors?: number | null
          total_raised?: number | null
          updated_at?: string | null
          verified?: boolean | null
          website_url?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "charities_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "charity_categories"
            referencedColumns: ["id"]
          },
        ]
      }
      charity_categories: {
        Row: {
          created_at: string | null
          description: string | null
          icon: string | null
          id: number
          name: string
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          icon?: string | null
          id?: number
          name: string
        }
        Update: {
          created_at?: string | null
          description?: string | null
          icon?: string | null
          id?: number
          name?: string
        }
        Relationships: []
      }
      donations: {
        Row: {
          amount: number
          charity_id: number | null
          created_at: string | null
          donation_date: string | null
          id: number
          status: string | null
          transaction_ids: number[] | null
          user_id: string | null
        }
        Insert: {
          amount: number
          charity_id?: number | null
          created_at?: string | null
          donation_date?: string | null
          id?: number
          status?: string | null
          transaction_ids?: number[] | null
          user_id?: string | null
        }
        Update: {
          amount?: number
          charity_id?: number | null
          created_at?: string | null
          donation_date?: string | null
          id?: number
          status?: string | null
          transaction_ids?: number[] | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "donations_charity_id_fkey"
            columns: ["charity_id"]
            isOneToOne: false
            referencedRelation: "charities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "donations_charity_id_fkey"
            columns: ["charity_id"]
            isOneToOne: false
            referencedRelation: "charities_with_categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "donations_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "user_donation_summary"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "donations_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      linked_accounts: {
        Row: {
          account_name: string | null
          account_subtype: string | null
          account_type: string | null
          auto_paused: boolean | null
          created_at: string | null
          deleted_at: string | null
          deletion_reason: string | null
          id: string
          is_active: boolean | null
          mask: string | null
          plaid_access_token: string
          plaid_account_id: string
          plaid_item_id: string
          rounding_enabled: boolean | null
          scheduled_pause_end: string | null
          scheduled_pause_reason: string | null
          scheduled_pause_start: string | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          account_name?: string | null
          account_subtype?: string | null
          account_type?: string | null
          auto_paused?: boolean | null
          created_at?: string | null
          deleted_at?: string | null
          deletion_reason?: string | null
          id?: string
          is_active?: boolean | null
          mask?: string | null
          plaid_access_token: string
          plaid_account_id: string
          plaid_item_id: string
          rounding_enabled?: boolean | null
          scheduled_pause_end?: string | null
          scheduled_pause_reason?: string | null
          scheduled_pause_start?: string | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          account_name?: string | null
          account_subtype?: string | null
          account_type?: string | null
          auto_paused?: boolean | null
          created_at?: string | null
          deleted_at?: string | null
          deletion_reason?: string | null
          id?: string
          is_active?: boolean | null
          mask?: string | null
          plaid_access_token?: string
          plaid_account_id?: string
          plaid_item_id?: string
          rounding_enabled?: boolean | null
          scheduled_pause_end?: string | null
          scheduled_pause_reason?: string | null
          scheduled_pause_start?: string | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      transactions: {
        Row: {
          card_last_four: string | null
          created_at: string | null
          id: number
          processed: boolean | null
          purchase_amount: number
          round_up_amount: number
          transaction_date: string | null
          user_id: string | null
          vendor_name: string
        }
        Insert: {
          card_last_four?: string | null
          created_at?: string | null
          id?: number
          processed?: boolean | null
          purchase_amount: number
          round_up_amount: number
          transaction_date?: string | null
          user_id?: string | null
          vendor_name: string
        }
        Update: {
          card_last_four?: string | null
          created_at?: string | null
          id?: number
          processed?: boolean | null
          purchase_amount?: number
          round_up_amount?: number
          transaction_date?: string | null
          user_id?: string | null
          vendor_name?: string
        }
        Relationships: [
          {
            foreignKeyName: "transactions_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "user_donation_summary"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "transactions_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      user_charity_follows: {
        Row: {
          allocation_percentage: number | null
          charity_id: number | null
          created_at: string | null
          id: number
          user_id: string | null
        }
        Insert: {
          allocation_percentage?: number | null
          charity_id?: number | null
          created_at?: string | null
          id?: number
          user_id?: string | null
        }
        Update: {
          allocation_percentage?: number | null
          charity_id?: number | null
          created_at?: string | null
          id?: number
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "user_charity_follows_charity_id_fkey"
            columns: ["charity_id"]
            isOneToOne: false
            referencedRelation: "charities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_charity_follows_charity_id_fkey"
            columns: ["charity_id"]
            isOneToOne: false
            referencedRelation: "charities_with_categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_charity_follows_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "user_donation_summary"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "user_charity_follows_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      users: {
        Row: {
          avatar_url: string | null
          created_at: string | null
          email: string
          full_name: string | null
          id: string
          monthly_goal: number | null
          total_donated: number | null
          updated_at: string | null
        }
        Insert: {
          avatar_url?: string | null
          created_at?: string | null
          email: string
          full_name?: string | null
          id: string
          monthly_goal?: number | null
          total_donated?: number | null
          updated_at?: string | null
        }
        Update: {
          avatar_url?: string | null
          created_at?: string | null
          email?: string
          full_name?: string | null
          id?: string
          monthly_goal?: number | null
          total_donated?: number | null
          updated_at?: string | null
        }
        Relationships: []
      }
    }
    Views: {
      charities_with_categories: {
        Row: {
          category_icon: string | null
          category_id: number | null
          category_name: string | null
          created_at: string | null
          description: string | null
          ein: string | null
          id: number | null
          impact_statement: string | null
          logo_url: string | null
          name: string | null
          rating: number | null
          total_donors: number | null
          total_raised: number | null
          updated_at: string | null
          verified: boolean | null
          website_url: string | null
        }
        Relationships: [
          {
            foreignKeyName: "charities_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "charity_categories"
            referencedColumns: ["id"]
          },
        ]
      }
      user_donation_summary: {
        Row: {
          charities_supported: number | null
          full_name: string | null
          monthly_goal: number | null
          total_donated_this_month: number | null
          total_round_ups_this_month: number | null
          user_id: string | null
        }
        Relationships: []
      }
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
